﻿#include "bot_ai.h"
#include "botdpstracker.h"
#include "bot_Events.h"
#include "botmgr.h"
#include "botdatamgr.h"
#include "bpet_ai.h"
#include "Chat.h"
#include "Config.h"
#include "GroupMgr.h"
#include "GridNotifiers.h"
#include "GridNotifiersImpl.h"
#include "Group.h"
#include "Language.h"
#include "MapManager.h"
#include "ObjectMgr.h"
#include "Player.h"
#include "ScriptMgr.h"
#include "SpellAuraEffects.h"
#include "../Custom/UnitMod/CharMod/CharMod.h"
#include "../Custom/DataLoader/DataLoader.h"
#include "../Custom/botspell/botspell.h"
#include "../Custom/MapMod/MapMod.h"
/*
Npc Bot Manager by Trickerer (<EMAIL>)
Player NpcBots management
TODO: Move creature hooks here
*/
#pragma execution_character_set("utf-8")
//config
uint8 _basefollowdist;
uint8 _maxNpcBots;
uint8 _maxClassNpcBots;
uint8 _xpReductionNpcBots;
uint8 _healTargetIconFlags;
uint8 _tankingTargetIconFlags;
uint8 _dpsTargetIconFlags;
//int32 _botInfoPacketsLimit;
uint32 _npcBotsCost;
uint32 _npcBotUpdateDelayBase;
uint32 _npcBotOwnerExpireTime;
bool _enableNpcBots;
bool _enableNpcBotsDungeons;
bool _enableNpcBotsRaids;
bool _enableNpcBotsBGs;
bool _enableNpcBotsArenas;
bool _enableDungeonFinder;
bool _limitNpcBotsDungeons;
bool _limitNpcBotsRaids;
bool _botPvP;
bool _botMovementFoodInterrupt;
bool _displayEquipment;
bool _showCloak;
bool _showHelm;
bool _sendEquipListItems;
bool _enableclass_blademaster;
bool _enableclass_sphynx;
bool _enableclass_archmage;
bool _enableclass_dreadlord;
bool _enableclass_spellbreaker;
bool _enableclass_darkranger;
bool _botStatLimits;
float _botStatLimits_dodge;
float _botStatLimits_parry;
float _botStatLimits_block;
float _botStatLimits_crit;
float _mult_dmg_physical;
float _mult_dmg_spell;
float _mult_healing;

bool __firstload = true;

BotMgr::BotMgr(Player* const master) : _owner(master), _dpstracker(new DPSTracker())
{
    //LoadConfig(); already loaded (MapManager.cpp)
    _followdist = _basefollowdist;
    _exactAttackRange = 0;
    _attackRangeMode = BOT_ATTACK_RANGE_SHORT;
    _botsHidden = false;

    _dpstracker->SetOwner(master->GetGUIDLow());
    master->SetBotMgr(this);
}
BotMgr::~BotMgr()
{
    delete _dpstracker;
}

void BotMgr::Initialize()
{
    LoadConfig();

    if (!_enableNpcBots)
        return;

    BotDataMgr::LoadNpcBots();
    bot_ai::InitBotCustomSpells();
}

void BotMgr::ReloadConfig()
{
    LoadConfig(true);
}

void BotMgr::LoadConfig(bool reload)
{
    if (__firstload)
        __firstload = false;
    else if (!reload)
        return;

    _enableNpcBots          = sConfigMgr->GetBoolDefault("NpcBot.Enable", true);
    _maxNpcBots             = sConfigMgr->GetIntDefault("NpcBot.MaxBots", 1);
    _maxClassNpcBots        = sConfigMgr->GetIntDefault("NpcBot.MaxBotsPerClass", 1);
    _basefollowdist         = sConfigMgr->GetIntDefault("NpcBot.BaseFollowDistance", 30);
    _xpReductionNpcBots     = sConfigMgr->GetIntDefault("NpcBot.XpReduction", 0);
    _healTargetIconFlags    = sConfigMgr->GetIntDefault("NpcBot.HealTargetIconsMask", 0);
    _tankingTargetIconFlags = sConfigMgr->GetIntDefault("NpcBot.TankTargetIconMask", 0);
    _dpsTargetIconFlags     = sConfigMgr->GetIntDefault("NpcBot.DPSTargetIconMask", 0);
    _mult_dmg_physical      = sConfigMgr->GetFloatDefault("NpcBot.Mult.Damage.Physical", 1.0f);
    _mult_dmg_spell         = sConfigMgr->GetFloatDefault("NpcBot.Mult.Damage.Spell", 1.0f);
    _mult_healing           = sConfigMgr->GetFloatDefault("NpcBot.Mult.Healing", 1.0f);
    _enableNpcBotsDungeons  = sConfigMgr->GetBoolDefault("NpcBot.Enable.Dungeon", true);
    _enableNpcBotsRaids     = sConfigMgr->GetBoolDefault("NpcBot.Enable.Raid", false);
    _enableNpcBotsBGs       = sConfigMgr->GetBoolDefault("NpcBot.Enable.BG", false);
    _enableNpcBotsArenas    = sConfigMgr->GetBoolDefault("NpcBot.Enable.Arena", false);
    _enableDungeonFinder    = sConfigMgr->GetBoolDefault("NpcBot.Enable.DungeonFinder", true);
    _limitNpcBotsDungeons   = sConfigMgr->GetBoolDefault("NpcBot.Limit.Dungeon", true);
    _limitNpcBotsRaids      = sConfigMgr->GetBoolDefault("NpcBot.Limit.Raid", true);
    //_botInfoPacketsLimit    = sConfigMgr->GetIntDefault("NpcBot.InfoPacketsLimit", -1);
    _npcBotsCost            = sConfigMgr->GetIntDefault("NpcBot.Cost", 1000000);
    _npcBotUpdateDelayBase  = sConfigMgr->GetIntDefault("NpcBot.UpdateDelay.Base", 0);
    _npcBotOwnerExpireTime  = sConfigMgr->GetIntDefault("NpcBot.OwnershipExpireTime", 0);
    _botPvP                 = sConfigMgr->GetBoolDefault("NpcBot.PvP", true);
    _botMovementFoodInterrupt=sConfigMgr->GetBoolDefault("NpcBot.Movements.InterruptFood", false);
    _displayEquipment       = sConfigMgr->GetBoolDefault("NpcBot.EquipmentDisplay.Enable", true);
    _showCloak              = sConfigMgr->GetBoolDefault("NpcBot.EquipmentDisplay.ShowCloak", true);
    _showHelm               = sConfigMgr->GetBoolDefault("NpcBot.EquipmentDisplay.ShowHelm", false);
    _sendEquipListItems     = sConfigMgr->GetBoolDefault("NpcBot.Gossip.ShowEquipmentListItems", false);
    _enableclass_blademaster= sConfigMgr->GetBoolDefault("NpcBot.NewClasses.Blademaster.Enable", true);
    _enableclass_sphynx     = sConfigMgr->GetBoolDefault("NpcBot.NewClasses.ObsidianDestroyer.Enable", true);
    _enableclass_archmage   = sConfigMgr->GetBoolDefault("NpcBot.NewClasses.Archmage.Enable", true);
    _enableclass_dreadlord  = sConfigMgr->GetBoolDefault("NpcBot.NewClasses.Dreadlord.Enable", true);
    _enableclass_spellbreaker=sConfigMgr->GetBoolDefault("NpcBot.NewClasses.SpellBreaker.Enable", true);
    _enableclass_darkranger = sConfigMgr->GetBoolDefault("NpcBot.NewClasses.DarkRanger.Enable", true);
    _botStatLimits          = sConfigMgr->GetBoolDefault("NpcBot.Stats.Limits.Enable", false);
    _botStatLimits_dodge    = sConfigMgr->GetFloatDefault("NpcBot.Stats.Limits.Dodge", 95.0f);
    _botStatLimits_parry    = sConfigMgr->GetFloatDefault("NpcBot.Stats.Limits.Parry", 95.0f);
    _botStatLimits_block    = sConfigMgr->GetFloatDefault("NpcBot.Stats.Limits.Block", 95.0f);
    _botStatLimits_crit     = sConfigMgr->GetFloatDefault("NpcBot.Stats.Limits.Crit", 95.0f);

    //limits
    _mult_dmg_physical      = std::max<float>(_mult_dmg_physical, 0.1f);
    _mult_dmg_spell         = std::max<float>(_mult_dmg_spell, 0.1f);
    _mult_healing           = std::max<float>(_mult_healing,   0.1f);
    _mult_dmg_physical      = std::min<float>(_mult_dmg_physical, 10.f);
    _mult_dmg_spell         = std::min<float>(_mult_dmg_spell, 10.f);
    _mult_healing           = std::min<float>(_mult_healing,   10.f);
}

uint8 BotMgr::GetNpcBotsCount() const
{
    //if (!inWorldOnly)
        return _bots.size();

    //CRITICAL SECTION
    //inWorldOnly is only for one-shot cases (opcodes, etc.)
    //maybe convert to (bot && bot->isInWorld()) ?
    //uint8 count = 0;
    //for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    //    if (ObjectAccessor::GetObjectInWorld(itr->first, (Creature*)NULL))
    //        ++count;
    //return count;
}

uint8 BotMgr::GetNpcBotsCountByRole(uint32 roles) const
{
    uint8 count = 0;
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        if (itr->second && (roles & itr->second->GetBotRoles()))
            ++count;
    return count;
}

uint8 BotMgr::GetNpcBotSlot(Creature const* bot) const
{
    uint8 count = 0;
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        ++count;
        if (itr->second == bot)
            return count;
    }
    return 1;
}

uint8 BotMgr::GetNpcBotSlotByRole(uint32 roles, Creature const* bot) const
{
    uint8 count = 0;
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        if (roles & itr->second->GetBotRoles())
        {
            if (!(roles == BOT_ROLE_DPS && (itr->second->GetBotRoles() & BOT_ROLE_TANK)))
                ++count;
            if (itr->second == bot)
                return count;
        }
    }
    return 1;
}

uint32 BotMgr::GetAllNpcBotsClassMask() const
{
    uint32 classMask = 0;
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        classMask |= (1 << (itr->second->GetBotClass() - 1));

    return classMask;
}

bool BotMgr::IsNpcBotModEnabled()
{
    return _enableNpcBots;
}

bool BotMgr::IsNpcBotDungeonFinderEnabled()
{
    return _enableDungeonFinder;
}

bool BotMgr::DisplayEquipment()
{
    return _displayEquipment;
}

bool BotMgr::ShowEquippedCloak()
{
    return _showCloak;
}

bool BotMgr::ShowEquippedHelm()
{
    return _showHelm;
}

bool BotMgr::SendEquipListItems()
{
    return _sendEquipListItems;
}

bool BotMgr::IsClassEnabled(uint8 m_class)
{
    switch (m_class)
    {
        case BOT_CLASS_BM:
            return _enableclass_blademaster;
        case BOT_CLASS_SPHYNX:
            return _enableclass_sphynx;
        case BOT_CLASS_ARCHMAGE:
            return _enableclass_archmage;
        case BOT_CLASS_DREADLORD:
            return _enableclass_dreadlord;
        case BOT_CLASS_SPELLBREAKER:
            return _enableclass_spellbreaker;
        case BOT_CLASS_DARK_RANGER:
            return _enableclass_darkranger;
        default:
            return true;
    }
}

bool BotMgr::IsBotStatsLimitsEnabled()
{
    return _botStatLimits;
}
bool BotMgr::IsPvPEnabled()
{
    return _botPvP;
}
bool BotMgr::IsFoodInterruptedByMovement()
{
    return _botMovementFoodInterrupt;
}
uint8 BotMgr::GetMaxClassBots()
{
    return _maxClassNpcBots;
}
uint8 BotMgr::GetHealTargetIconFlags()
{
    return _healTargetIconFlags;
}
uint8 BotMgr::GetTankTargetIconFlags()
{
    return _tankingTargetIconFlags;
}
uint8 BotMgr::GetDPSTargetIconFlags()
{
    return _dpsTargetIconFlags;
}
uint32 BotMgr::GetBaseUpdateDelay()
{
    return _npcBotUpdateDelayBase;
}
uint32 BotMgr::GetOwnershipExpireTime()
{
    return _npcBotOwnerExpireTime;
}
float BotMgr::GetBotStatLimitDodge()
{
    return _botStatLimits_dodge;
}
float BotMgr::GetBotStatLimitParry()
{
    return _botStatLimits_parry;
}
float BotMgr::GetBotStatLimitBlock()
{
    return _botStatLimits_block;
}
float BotMgr::GetBotStatLimitCrit()
{
    return _botStatLimits_crit;
}

uint8 BotMgr::GetNpcBotXpReduction()
{
    return _xpReductionNpcBots;
}

uint8 BotMgr::GetMaxNpcBots()
{
    return _maxNpcBots <= MAXRAIDSIZE - 1 ? _maxNpcBots : MAXRAIDSIZE - 1;
}

//int32 BotMgr::GetBotInfoPacketsLimit()
//{
//    return _botInfoPacketsLimit;
//}

bool BotMgr::LimitBots(Map const* map)
{
    if (_limitNpcBotsDungeons && map->IsNonRaidDungeon())
        return true;
    if (_limitNpcBotsRaids && map->IsRaid())
        return true;

    return false;
}

void BotMgr::Update(uint32 diff)
{
    //remove temp bots from bot map before updating it
    while (!_removeList.empty())
    {
        std::list<uint64>::iterator itr = _removeList.begin();

        BotMap::iterator bitr = _bots.find(*itr);
        ASSERT(bitr != _bots.end());
        _bots.erase(bitr);

        _removeList.erase(itr);
    }

    _dpstracker->Update(diff);

    if (!HaveBot())
        return;

    //uint64 guid;
    Creature* bot;
    bot_ai* ai;
    bool partyCombat = IsPartyInCombat();
    bool restrictBots = RestrictBots(NULL, false);

    _aoespots.clear();
    if (partyCombat)
        bot_ai::CalculateAoeSpots(_owner, _aoespots);

    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        //guid = itr->first;
        bot = itr->second;
        ai = bot->GetBotAI();

        if (ai->IAmFree())
            continue;

        if (!bot->IsInWorld())
        {
            ai->CommonTimers(diff);
            continue;
        }

        if (partyCombat == false)
            ai->UpdateReviveTimer(diff);

        bot->IsAIEnabled = true;

        if (ai->GetReviveTimer() <= diff)
        {
            if (bot->IsInWorld() && !bot->IsAlive() && _owner->IsAlive() && !_owner->IsInCombat() &&
                !_owner->IsBeingTeleported() && !_owner->InArena() && !_owner->IsInFlight() &&
                !_owner->HasFlag(UNIT_FIELD_FLAGS_2, UNIT_FLAG2_FEIGN_DEATH) &&
                !_owner->HasInvisibilityAura() && !_owner->HasStealthAura())
            {
                _reviveBot(bot);
                continue;
            }

            ai->SetReviveTimer(urand(1000, 5000));
        }

        if (_owner->IsAlive() && (bot->IsAlive() || restrictBots) && !ai->IsTempBot() && !ai->IsDuringTeleport() &&
            (restrictBots || bot->GetMap() != _owner->GetMap() ||
            (!bot->GetBotAI()->HasBotCommandState(BOT_COMMAND_STAY) && _owner->GetDistance(bot) > SIZE_OF_GRIDS)))
        {
            //_owner->m_Controlled.erase(bot);
            TeleportBot(bot, _owner->GetMap(), _owner);
            continue;
        }

        ai->canUpdate = true;
        bot->Update(diff);
        ai->canUpdate = false;
    }
}

bool BotMgr::RestrictBots(Creature const* bot, bool add) const
{
    if (!_owner->FindMap())
        return true;

    if (_owner->IsInFlight())
        return true;

    if (_botsHidden)
        return true;

    Map const* currMap = _owner->GetMap();

    if ((!_enableNpcBotsBGs && currMap->IsBattleground()) ||
        (!_enableNpcBotsArenas && currMap->IsBattleArena()) ||
		(!_enableNpcBotsDungeons && currMap->IsNonRaidDungeon()) || (_owner->GetMapId() == 631 && _owner->GunShipBot) || (sDataLoader->cheakRestrictBot(_owner->GetMapId(), _owner->GetZoneId(), _owner->GetAreaId())) ||
        (!_enableNpcBotsRaids && currMap->IsRaid()))
        return true;

    if (LimitBots(currMap))
    {
        //if bot is not in instance group - deny (only if trying to teleport to instance)
        if (add)
            if (!_owner->GetGroup() || !_owner->GetGroup()->IsMember(bot->GetGUID()))
                return true;

        InstanceMap const* map = currMap->ToInstanceMap();
        if (map->GetPlayersCountExceptGMs() + uint32(add) > map->GetMaxPlayers())
            return true;
    }

    return false;
}

bool BotMgr::IsPartyInCombat() const
{
	if (!_owner)
		return false;

    if (_owner->IsInCombat())
        return true;

    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
		if (!itr->second)
			continue;

        if (!itr->second->IsInWorld())
            continue;
        if (itr->second->IsInCombat())
            return true;
        if (Unit const* pet = itr->second->GetBotsPet())
            if (pet->IsInCombat())
                return true;
    }

    return false;
}

bool BotMgr::HasBotClass(uint8 botclass) const
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        if (itr->second->GetBotClass() == botclass)
            return true;

    return false;
}

bool BotMgr::HasBotPetType(uint32 petType) const
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        if (itr->second->GetBotsPet() && itr->second->GetBotAI()->GetAIMiscValue(BOTAI_MISC_PET_TYPE) == petType)
            return true;

    return false;
}

void BotMgr::_reviveBot(Creature* bot, WorldLocation* dest)
{
    if (bot->IsAlive())
        return;

    if (!bot->GetBotAI()->IAmFree())
    {
        if (!dest)
            bot->CastSpell(bot, COSMETIC_RESURRECTION, false);

        if (!dest)
            dest = bot->GetBotOwner();

        bot->NearTeleportTo(dest->GetPositionX(), dest->GetPositionY(), dest->GetPositionZ(), dest->GetOrientation());
        //some weird pos manipulation
        if (dest != bot)
            bot->Relocate(dest);
    }
	bot->GetBotAI()->HandleBotExMorph();
    //bot->SetDisplayId(bot->GetNativeDisplayId());
    bot->SetUInt32Value(UNIT_NPC_FLAGS, bot->GetCreatureTemplate()->npcflag);
    bot->ClearUnitState(uint32(UNIT_STATE_ALL_STATE));
    bot->RemoveFlag(UNIT_FIELD_FLAGS, uint32(-1));
    bot->SetPvP(bot->GetBotOwner()->IsPvP());
    bot->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PVP_ATTACKABLE);
    bot->setDeathState(ALIVE);
    //bot->GetBotAI()->Reset();
    bot->GetBotAI()->SetShouldUpdateStats();

    bot->SetHealth(bot->GetMaxHealth() / 4); //25% of max health
    if (bot->GetMaxPower(POWER_MANA) > 1)
        bot->SetPower(POWER_MANA, bot->GetMaxPower(POWER_MANA) / 4); //25% of max mana

    if (!bot->GetBotAI()->IAmFree() && !bot->GetBotAI()->HasBotCommandState(BOT_COMMAND_MASK_UNMOVING))
        bot->GetBotAI()->SetBotCommandState(BOT_COMMAND_FOLLOW, true);
}

Creature* BotMgr::GetBot(uint64 guid) const
{
    BotMap::const_iterator itr = _bots.find(guid);
    return itr != _bots.end() ? itr->second : NULL;
}

Creature* BotMgr::GetBotByName(std::string const& name) const
{
    std::wstring wname;
    if (Utf8toWStr(name, wname))
    {
        wstrToLower(wname);
        for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        {
            if (!itr->second)
                continue;

            std::string basename = itr->second->GetName();
            if (CreatureLocale const* creatureInfo = sObjectMgr->GetCreatureLocale(itr->second->GetEntry()))
            {
                uint32 loc = _owner->GetSession()->GetSessionDbLocaleIndex();
                if (creatureInfo->Name.size() > loc && !creatureInfo->Name[loc].empty())
                    basename = creatureInfo->Name[loc];
            }

            std::wstring wbname;
            if (!Utf8toWStr(basename, wbname))
                continue;

            wstrToLower(wbname);
            if (wbname == wname)
                return itr->second;
        }
    }

    return NULL;
}

void BotMgr::OnOwnerSetGameMaster(bool on)
{
    Creature* bot;
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        bot = itr->second;
        if (!bot)
            continue;

        bot->setFaction(_owner->getFaction());
        bot->getHostileRefManager().setOnlineOfflineState(!on);
        bot->SetByteValue(UNIT_FIELD_BYTES_2, 1, _owner->GetByteValue(UNIT_FIELD_BYTES_2, 1)); //pvp state

        if (on && bot->IsInWorld())
            bot->CombatStop(true);

        if (Unit* pet = bot->GetBotsPet())
        {
            pet->setFaction(_owner->getFaction());
            pet->getHostileRefManager().setOnlineOfflineState(!on);
            pet->SetByteValue(UNIT_FIELD_BYTES_2, 1, _owner->GetByteValue(UNIT_FIELD_BYTES_2, 1)); //pvp state

            if (on)
                pet->CombatStop(true);
        }
    }
}

void BotMgr::OnTeleportFar(uint32 mapId, float x, float y, float z, float ori)
{
    Map* newMap = sMapMgr->CreateBaseMap(mapId);
    Creature* bot;
    Position pos;
    pos.Relocate(x, y, z, ori);

    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        bot = itr->second;
        ASSERT(bot && "BotMgr::OnTeleportFar(): bot does not exist!!!");

        if (bot->IsTempBot())
            continue;

        //_owner->m_Controlled.erase(bot);
        TeleportBot(bot, newMap, &pos);
    }
}

void BotMgr::_teleportBot(Creature* bot, Map* newMap, float x, float y, float z, float ori)
{
    ASSERT(bot->GetBotAI());
    bot->GetBotAI()->AbortTeleport();

    bot->GetBotAI()->KillEvents(true);

    if (bot->GetVehicle())
        bot->ExitVehicle();

    if (bot->IsInWorld())
    {
        //bot->MonsterWhisper("teleport...", bot->GetBotAI()->GetBotOwnerGuid());
        bot->CastSpell(bot, COSMETIC_TELEPORT_EFFECT, true);
    }

    if (Map* mymap = bot->FindMap())
    {
        bot->BotStopMovement();
        bot->GetBotAI()->UnsummonAll();

        ////start Unit::CleanupBeforeRemoveFromMap()
        bot->InterruptNonMeleeSpells(true);
        bot->IsAIEnabled = false;
        if (bot->IsInWorld())
            bot->RemoveFromWorld();

        ASSERT(bot->GetGUID());

        //RemoveAllAuras();
        bot->RemoveAllGameObjects();

        bot->m_Events.KillAllEvents(false);                      // non-delatable (currently casted spells) will not deleted now but it will deleted at call in Map::RemoveAllObjectsInRemoveList
        bot->CombatStop();
        bot->ClearComboPointHolders();
        //bot->DeleteThreatList();
        bot->getHostileRefManager().setOnlineOfflineState(false);

        //bot->CleanupBeforeRemoveFromMap(false);

        mymap->RemoveFromMap(bot, false);
    }

    if (bot->IsFreeBot()/* || bot->GetBotOwner()->GetSession()->isLogingOut()*/)
    {
        //bot->FarTeleportTo(newMap, x, y, z, ori);

        //Creature::FarTeleportTo()
        //{
        //CleanupBeforeRemoveFromMap(false); //done above
        //GetMap()->RemoveFromMap(this, false); //done above
        //Relocate(X, Y, Z, O);
        //SetMap(map);
        //GetMap()->AddToMap(this);
        //}
        bot->Relocate(x, y, z, ori);
        bot->SetMap(newMap);
        bot->GetMap()->AddToMap(bot);
        //end Creature::FarTeleportTo()

        //bot->SetAI(oldAI);
        bot->IsAIEnabled = true;
        return;
    }

    //update group member online state
    if (Group* gr = bot->GetBotOwner()->GetGroup())
        if (gr->IsMember(bot->GetGUID()))
            gr->SendUpdate();

    //bot->Relocate(x, y, z);
    TeleportFinishEvent* finishEvent = new TeleportFinishEvent(bot->GetBotAI()/*, newMap->GetId(), newMap->GetInstanceId(), x, y, z, ori*/);
    bot->GetBotAI()->GetEvents()->AddEvent(finishEvent, bot->GetBotAI()->GetEvents()->CalculateTime(urand(500, 1000)));
    bot->GetBotAI()->SetTeleportFinishEvent(finishEvent);
}

void BotMgr::TeleportBot(Creature* bot, Map* newMap, Position* pos)
{
    _teleportBot(bot, newMap, pos->GetPositionX(), pos->GetPositionY(), pos->GetPositionZ(), pos->GetOrientation());
}

void BotMgr::CleanupsBeforeBotDelete(uint64 guid, uint8 removetype)
{
    BotMap::const_iterator itr = _bots.find(guid);
    ASSERT(itr != _bots.end() && "Trying to remove bot which does not belong to this botmgr(b)!!");
    //ASSERT(_owner->IsInWorld() && "Trying to remove bot while not in world(b)!!");

    Creature* bot = itr->second;

    //don't allow removing bots while they are teleporting
    if (!bot->IsInWorld())
    {
        bot->GetBotAI()->AbortTeleport();
        //if (!bot->IsInWorld())
        //{
        //    sLog->outError("BotMgr::CleanupsBeforeBotDelete(): Failed to abort %s's teleport! Still out of world!", bot->GetName().c_str());
        //    ASSERT(false);
        //}
    }

    if (bot->GetVehicle())
        bot->ExitVehicle();

    RemoveBotFromGroup(bot);

    //remove any summons
    bot->GetBotAI()->UnsummonAll();

    //cancel all orders
    if (bot->GetBotAI()->HasOrders() || bot->GetBotAI()->HasBotCommandState(BOT_COMMAND_ISSUED_ORDER))
    {
        sLog->outError("BotMgr::CleanupsBeforeBotDelete(): %s still has orders. Removing...", bot->GetName().c_str());
        bot->GetBotAI()->CancelAllOrders();
    }

    ASSERT(bot->GetOwnerGUID() == _owner->GetGUID());
    bot->SetOwnerGUID(0);
    //_owner->m_Controlled.erase(bot);
    bot->m_ControlledByPlayer = false;
    //bot->RemoveFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PVP_ATTACKABLE);
    bot->SetByteValue(UNIT_FIELD_BYTES_2, 1, 0);
    bot->SetCreatorGUID(0);

    Map* map = bot->FindMap();
    if (!map || map->IsDungeon())
        bot->RemoveFromWorld();
}

void BotMgr::_addBotToRemoveList(uint64 guid)
{
    _removeList.push_back(guid);
}

void BotMgr::RemoveAllBots(uint8 removetype)
{
    while (!_bots.empty())
	{
		RemoveBot(_bots.begin()->first, removetype);

		if (_botsHidden)
			_botsHidden = false;
	}
}
//Bot is being abandoned by player
void BotMgr::RemoveBot(uint64 guid, uint8 removetype)
{
    BotMap::const_iterator itr = _bots.find(guid);
    ASSERT(itr != _bots.end() && "Trying to remove bot which does not belong to this botmgr(a)!!");
    //ASSERT(_owner->IsInWorld() && "Trying to remove bot while not in world(a)!!");

    //trying to remove temp bot second time means removing all bots
    //just erase from bots because already cleaned up
    for (std::list<uint64>::iterator it = _removeList.begin(); it != _removeList.end(); ++it)
    {
        if (*it == guid)
        {
            _removeList.erase(it);
            _bots.erase(itr);
            return;
        }
    }

    Creature* bot = itr->second;
    CleanupsBeforeBotDelete(guid, removetype);

    ////remove control bar
    //if (GetNpcBotsCount() <= 1 && !_owner->GetPetGUID() && _owner->m_Controlled.empty())
    //    _owner->SendRemoveControlBar();

    if (bot->GetBotAI()->IsTempBot())
    {
        //bot->GetBotAI()->OnBotDespawn(bot); //send to self
        _addBotToRemoveList(guid);
        return;
    }

    _bots.erase(itr);

	bot->CombatStop();
    bot->GetBotAI()->ResetBotAI(removetype == BOT_REMOVE_DISMISS ? BOTAI_RESET_DISMISS : BOTAI_RESET_LOGOUT);

    bot->setFaction(bot->GetCreatureTemplate()->faction);
    bot->SetLevel(bot->GetCreatureTemplate()->minlevel);

    if (removetype == BOT_REMOVE_DISMISS)
    {
        //uint32 newOwner = 0;
        //BotDataMgr::UpdateNpcBotData(bot->GetDBTableGUIDLow(), NPCBOT_UPDATE_OWNER, &newOwner);
    }

    bot->GetBotAI()->Reset();
	bot->GetBotAI()->SetCheakSummon(false);
	Map * map = bot->GetMap();
	if (map)
		map->RemoveFromMap(bot, false);
}

BotAddResult BotMgr::AddBot(Creature* bot, bool takeMoney)
{
    ASSERT(bot->IsNPCBot());
    ASSERT(bot->GetBotAI() != NULL);

    bool temporary = bot->GetBotAI()->IsTempBot();

    if (!_enableNpcBots)
    {
        ChatHandler ch(_owner->GetSession());
        ch.SendSysMessage(bot_ai::LocalizedNpcText(GetOwner(), BOT_TEXT_BOTADDFAIL_DISABLED).c_str());
        return BOT_ADD_DISABLED;
    }
    if (GetBot(bot->GetGUID()))
        return BOT_ADD_ALREADY_HAVE; //Silent error, intended
    if (!bot->GetBotAI()->IAmFree())
    {
        ChatHandler ch(_owner->GetSession());
        ch.PSendSysMessage(bot_ai::LocalizedNpcText(GetOwner(), BOT_TEXT_BOTADDFAIL_OWNED).c_str(),
            bot->GetName().c_str(), bot->GetBotOwner()->GetName().c_str());
        return BOT_ADD_NOT_AVAILABLE;
    }
    if (bot->GetBotAI()->IsDuringTeleport())
    {
        ChatHandler ch(_owner->GetSession());
        ch.PSendSysMessage(bot_ai::LocalizedNpcText(GetOwner(), BOT_TEXT_BOTADDFAIL_TELEPORTED).c_str(), bot->GetName().c_str());
        return BOT_ADD_BUSY;
    }
    //if (!temporary && _owner->GetNpcBotsCount() >= GetMaxNpcBots())
	if (!temporary && _owner->GetNpcBotsCount() >= sCharMod->GetPlayerBotNum(_owner->GetSession()->GetAccountId()))
    {
        ChatHandler ch(_owner->GetSession());
        ch.PSendSysMessage(bot_ai::LocalizedNpcText(GetOwner(), BOT_TEXT_HIREFAIL_MAXBOTS).c_str(), GetMaxNpcBots());
        return BOT_ADD_MAX_EXCEED;
    }
    if (!temporary && HaveBot() && _maxClassNpcBots)
    {
        uint8 count = 0;
        for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
            if (itr->second->GetBotClass() == bot->GetBotClass())
                ++count;

        if (count >= _maxClassNpcBots)
        {
            ChatHandler ch(_owner->GetSession());
            ch.PSendSysMessage(bot_ai::LocalizedNpcText(GetOwner(), BOT_TEXT_HIREFAIL_MAXCLASSBOTS).c_str(), count, _maxClassNpcBots);
            return BOT_ADD_MAX_CLASS_EXCEED;
        }
    }
    //Map* curMap = _owner->GetMap();
    //if (!temporary && LimitBots(curMap))
    //{
    //    InstanceMap* map = curMap->ToInstanceMap();
    //    uint32 count = map->GetPlayersCountExceptGMs();
    //    if (count >= map->GetMaxPlayers())
    //    {
    //        ChatHandler ch(_owner->GetSession());
    //        ch.PSendSysMessage("Instance players limit exceed (%u of %u)", count, map->GetMaxPlayers());
    //        return BOT_ADD_INSTANCE_LIMIT;
    //    }
    //}
    if (!temporary && takeMoney)
    {
        uint32 cost = GetNpcBotCost(_owner->getLevel(), bot->GetBotClass());
        if (!_owner->HasEnoughMoney(cost))
        {
            ChatHandler ch(_owner->GetSession());
            std::string str = bot_ai::LocalizedNpcText(GetOwner(), BOT_TEXT_HIREFAIL_COST) + " (";
            str += GetNpcBotCostStr(_owner->getLevel(), bot->GetBotClass());
            str += ")!";
            ch.SendSysMessage(str.c_str());
            return BOT_ADD_CANT_AFFORD;
        }

        _owner->ModifyMoney(-(int32(cost)));
    }

    if (!bot->IsAlive())
        _reviveBot(bot);

    bot->GetBotAI()->UnsummonAll();

    _bots[bot->GetGUID()] = bot;

    ASSERT(!bot->GetOwnerGUID());
    bot->SetOwnerGUID(_owner->GetGUID());
    bot->SetCreatorGUID(_owner->GetGUID()); //needed in case of FFAPVP
    //_owner->m_Controlled.insert(bot);
    bot->m_ControlledByPlayer = true;
    bot->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PVP_ATTACKABLE);
    bot->SetByteValue(UNIT_FIELD_BYTES_2, 1, _owner->GetByteValue(UNIT_FIELD_BYTES_2, 1));
    bot->setFaction(_owner->getFaction());
    bot->SetPhaseMask(_owner->GetPhaseMask(), true);

    bot->GetBotAI()->SetBotOwner(_owner);

    bot->GetBotAI()->Reset();

    if (!temporary)
    {
        bot->GetBotAI()->SetBotCommandState(BOT_COMMAND_FOLLOW, true);
        if (bot->GetBotAI()->HasRole(BOT_ROLE_PARTY))
            AddBotToGroup(bot);

        uint32 newOwner = _owner->GetGUIDLow();
        BotDataMgr::UpdateNpcBotData(bot->GetDBTableGUIDLow(), NPCBOT_UPDATE_OWNER, &newOwner);
    }
	
	const FBotSpellMap * custspell = sBotSpell->FindBotSpell(bot->GetEntry());
	if (custspell)
	{
		if (!custspell->spellids.empty())
		{
			Tokenizer tokspells(custspell->spellids, ' ');
			for (Tokenizer::size_type i = 0; i != tokspells.size(); ++i)
				bot->AddAura(atoi(tokspells[i]), bot);
		}
	}

	bot->GetBotAI()->SetCheakSummon(true);

    return BOT_ADD_SUCCESS;
}

bool BotMgr::AddBotToGroup(Creature* bot)
{
    ASSERT(GetBot(bot->GetGUID()));

	//if (sMapMod->IsDisable(_owner->GetMapId()))
	//{
	//	_owner->GetSession()->SendNotification("此地图中你无法组队！");
	//	return false;
	//}

    Group* gr = _owner->GetGroup();
    if (gr)
    {
        if (gr->IsMember(bot->GetGUID()))
            return true;

        if (gr->IsFull())
        {
            if (!gr->isRaidGroup()) //non-raid group is full
                gr->ConvertToRaid();
            else
                return false;
        }
    }
    else
    {
        gr = new Group;
        if (!gr->Create(_owner))
        {
            delete gr;
            return false;
        }
        sGroupMgr->AddGroup(gr);
    }

    if (gr->AddMember((Player*)bot))
    {
        if (!bot->GetBotAI()->HasRole(BOT_ROLE_PARTY))
            bot->GetBotAI()->ToggleRole(BOT_ROLE_PARTY, true);

        return true;
    }

    return false;
}

bool BotMgr::RemoveBotFromGroup(Creature* bot)
{
    ASSERT(GetBot(bot->GetGUID()));

    Group* gr = _owner->GetGroup();
    if (!gr || !gr->IsMember(bot->GetGUID()))
        return false;

    if (bot->GetBotAI()->HasRole(BOT_ROLE_PARTY) && !_owner->GetSession()->PlayerLogout())
        bot->GetBotAI()->ToggleRole(BOT_ROLE_PARTY, true);

    //debug
    //if (gr->RemoveMember(bot->GetGUID()))
    //    sLog->outError("RemoveBotFromGroup(): bot %s removed from group", bot->GetName().c_str());
    //else
    //    sLog->outError("RemoveBotFromGroup(): RemoveMember() returned FALSE on bot %s", bot->GetName().c_str());

    gr->RemoveMember(bot->GetGUID());

    //if removed from group while in instance / bg then remove from world immediately
    if (bot->IsInWorld() && RestrictBots(bot, true))
        TeleportBot(bot, bot->GetMap(), bot);

    return true;
}

bool BotMgr::RemoveAllBotsFromGroup()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        RemoveBotFromGroup(itr->second);

    return true;
}

uint32 BotMgr::GetNpcBotCost(uint8 level, uint8 botclass)
{
    //assuming default 1000000
    //level 1: 1000
    //11 : 1666
    //15 : 8333
    //20 : 16666
    //30 : 33333
    //40 : 50000
    //rest is linear
    //rare / rareelite bots have their cost adjusted
    uint32 cost =
        level < 10 ? _npcBotsCost / 5000 : //2 silver
        level < 20 ? _npcBotsCost / 100 :  //1 gold
        level < 30 ? _npcBotsCost / 20 :   //5 gold
        level < 40 ? _npcBotsCost / 5 :    //20 gold
        (_npcBotsCost * level) / DEFAULT_MAX_LEVEL; //50 - 100 gold

    switch (botclass)
    {
        case BOT_CLASS_BM:
        case BOT_CLASS_ARCHMAGE:
        case BOT_CLASS_SPELLBREAKER:
            cost += cost; //200%
            break;
        case BOT_CLASS_SPHYNX:
        case BOT_CLASS_DREADLORD:
        case BOT_CLASS_DARK_RANGER:
            cost += cost * 4; //500%
            break;
        default:
            break;
    }

    return cost;
}

std::string BotMgr::GetNpcBotCostStr(uint8 level, uint8 botclass)
{
    std::ostringstream money;

    if (uint32 cost = GetNpcBotCost(level, botclass))
    {
        uint32 gold = uint32(cost / GOLD);
        cost -= (gold * GOLD);
        uint32 silver = uint32(cost / SILVER);
        cost -= (silver * SILVER);

        if (gold != 0)
            money << gold << " |TInterface\\Icons\\INV_Misc_Coin_01:8|t";
        if (silver != 0)
            money << silver << " |TInterface\\Icons\\INV_Misc_Coin_03:8|t";
        if (cost)
            money << cost << " |TInterface\\Icons\\INV_Misc_Coin_05:8|t";
    }

    return money.str();
}

void BotMgr::ReviveAllBots()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        _reviveBot(itr->second);
}

void BotMgr::SendBotCommandState(uint8 state)
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        itr->second->GetBotAI()->SetBotCommandState(state, true);
}

void BotMgr::RecallAllBots()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        if (itr->second->IsInWorld() && itr->second->IsAlive() && !bot_ai::CCed(itr->second, true))
            itr->second->GetMotionMaster()->MovePoint(_owner->GetMapId(), *_owner, false);
}

void BotMgr::RecallBot(Creature* bot)
{
    ASSERT(GetBot(bot->GetGUID()));

    if (bot->IsInWorld() && bot->IsAlive() && !bot_ai::CCed(bot, true))
        bot->GetMotionMaster()->MovePoint(_owner->GetMapId(), *_owner, false);
}

void BotMgr::KillAllBots()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        KillBot(itr->second);
}

void BotMgr::KillBot(Creature* bot)
{
    ASSERT(GetBot(bot->GetGUID()));

    if (bot->IsInWorld() && bot->IsAlive())
    {
        bot->setDeathState(JUST_DIED);
        bot->GetBotAI()->JustDied(bot);
        //bot->Kill(bot);
    }
}

void BotMgr::SetBotsShouldUpdateStats()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
        itr->second->GetBotAI()->SetShouldUpdateStats();
}

void BotMgr::UpdatePhaseForBots()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        itr->second->SetPhaseMask(_owner->GetPhaseMask(), itr->second->IsInWorld());
        if (itr->second->GetBotsPet())
            itr->second->GetBotsPet()->SetPhaseMask(_owner->GetPhaseMask(), true); //only if in world
    }
}

void BotMgr::UpdatePvPForBots()
{
    for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
    {
        itr->second->SetByteValue(UNIT_FIELD_BYTES_2, 1, _owner->GetByteValue(UNIT_FIELD_BYTES_2, 1));
        if (itr->second->GetBotsPet())
            itr->second->GetBotsPet()->SetByteValue(UNIT_FIELD_BYTES_2, 1, _owner->GetByteValue(UNIT_FIELD_BYTES_2, 1));
    }
}

void BotMgr::TrackDamage(Unit const* u, uint32 damage)
{
    _dpstracker->TrackDamage(u, damage);
}

uint32 BotMgr::GetDPSTaken(Unit const* u) const
{
    return _dpstracker->GetDPSTaken(u->GetGUID());
}

int32 BotMgr::GetHPSTaken(Unit const* unit) const
{
    if (!HaveBot())
        return 0;

    std::list<Unit*> unitList;
    Group const* gr = _owner->GetGroup();
    if (!gr)
    {
        if (_owner->HasUnitState(UNIT_STATE_CASTING))
            unitList.push_back(_owner);
        for (BotMap::const_iterator itr = _bots.begin(); itr != _bots.end(); ++itr)
            if (itr->second->GetTarget() == unit->GetGUID() && itr->second->HasUnitState(UNIT_STATE_CASTING))
                unitList.push_back(itr->second);
    }
    else
    {
        bool Bots = false;
        for (GroupReference const* itr = gr->GetFirstMember(); itr != NULL; itr = itr->next())
        {
            Player* player = itr->GetSource();
            if (player == NULL) continue;
            if (_owner->GetMap() != player->FindMap()) continue;
            if (!Bots)
                Bots = true;
            if (player->HasUnitState(UNIT_STATE_CASTING))
                unitList.push_back(player);
        }
        if (Bots)
        {
            for (GroupReference const* itr = gr->GetFirstMember(); itr != NULL; itr = itr->next())
            {
                if (itr->GetSource() == NULL) continue;
                if (_owner->GetMap() != itr->GetSource()->FindMap()) continue;

                if (itr->GetSource()->HaveBot())
                {
                    BotMap const* map = itr->GetSource()->GetBotMgr()->GetBotMap();
                    for (BotMap::const_iterator itr = map->begin(); itr != map->end(); ++itr)
                        if (itr->second->GetTarget() == unit->GetGUID() && itr->second->HasUnitState(UNIT_STATE_CASTING))
                            unitList.push_back(itr->second);
                }
            }
        }
    }

    int32 amount = 0;

    Unit* u;
    Spell const* spell;
    SpellInfo const* spellInfo;
    for (std::list<Unit*>::const_iterator itr = unitList.begin(); itr != unitList.end(); ++itr)
    {
        u = *itr;

        for (uint8 i = CURRENT_FIRST_NON_MELEE_SPELL; i != CURRENT_AUTOREPEAT_SPELL; ++i)
        {
            spell = u->GetCurrentSpell(CurrentSpellTypes(i));
            if (!spell)
                continue;

            uint64 targetGuid = spell->m_targets.GetObjectTargetGUID();
            if (!targetGuid || !IS_UNIT_GUID(targetGuid))
                continue;

            if (targetGuid != unit->GetGUID())
            {
                if (!gr || !gr->IsMember(unit->GetGUID()))
                    continue;
            }

            spellInfo = spell->GetSpellInfo();

            for (uint8 j = 0; j != MAX_SPELL_EFFECTS; ++j)
            {
                if (spellInfo->Effects[j].Effect != SPELL_EFFECT_HEAL)
                    continue;

                if (targetGuid != unit->GetGUID())
                {
                    if (spellInfo->Effects[j].TargetA.GetSelectionCategory() != TARGET_SELECT_CATEGORY_AREA)
                        continue;

                    //Targets t = spellInfo->Effects[j].TargetA.GetTarget();
                    //non-existing case
                    //if (t == TARGET_UNIT_CASTER_AREA_PARTY && !gr->SameSubGroup(u->GetGUID(), unit->GetGUID()))
                    //    continue;
                    Targets t = spellInfo->Effects[j].TargetB.GetTarget();
                    if (t == TARGET_UNIT_LASTTARGET_AREA_PARTY &&
                        !(GetBot(unit->GetGUID()) && GetBot(targetGuid)) &&
                        !gr->SameSubGroup(unit->GetGUID(), targetGuid))
                        continue;
                }

                int32 healing = u->SpellHealingBonusDone(const_cast<Unit*>(unit), spellInfo, spellInfo->Effects[0].CalcValue(u), HEAL);
				healing = const_cast<Unit*>(unit)->SpellHealingBonusTaken(u, spellInfo, healing, HEAL);

                if (i == CURRENT_CHANNELED_SPELL)
                    amount += healing / (spellInfo->Effects[j].Amplitude * 0.001f);
                else
                    amount += healing / (std::max<int32>(spell->GetTimer(), 1000) * 0.001f);

                //sLog->outError("BotMgr:pendingHeals: found %s's %s on %s in %u (%i, total %i)",
                //    u->GetName().c_str(), spellInfo->SpellName[0], target->GetName().c_str(), pheal->delay, healing, pheal->amount);
            }

            break;
        }
    }

    //HoTs
    Unit::AuraEffectList const& hots = unit->GetAuraEffectsByType(SPELL_AURA_PERIODIC_HEAL);
    for (Unit::AuraEffectList::const_iterator itr = hots.begin(); itr != hots.end(); ++itr)
        amount += (*itr)->GetAmount() / ((*itr)->GetAmplitude() * 0.001f);

    //if (amount != 0)
    //    sLog->outError("BotMgr:GetHPSTaken(): %s got %i)", unit->GetName().c_str(), amount);

    return amount;
}

void BotMgr::OnBotSpellGo(Unit const* caster, Spell const* spell, bool ok)
{
    if (caster->ToCreature()->GetBotAI())
        caster->ToCreature()->GetBotAI()->OnBotSpellGo(spell, ok);
    else if (caster->ToCreature()->GetBotPetAI())
        caster->ToCreature()->GetBotPetAI()->OnBotPetSpellGo(spell, ok);
}

void BotMgr::OnBotOwnerSpellGo(Unit const* caster, Spell const* spell, bool ok)
{
    BotMap const* bmap = caster->ToPlayer()->GetBotMgr()->GetBotMap();
    for (BotMap::const_iterator itr = bmap->begin(); itr != bmap->end(); ++itr)
    {
        if (Creature const* bot = itr->second)
        {
            bot->GetBotAI()->OnBotOwnerSpellGo(spell, ok);
            //if (Creature const* botpet = bot->GetBotsPet())
            //    botpet->GetBotAI()->OnBotPetOwnerSpellGo(spell, ok);
        }
    }
}

void BotMgr::OnVehicleSpellGo(Unit const* caster, Spell const* spell, bool ok)
{
    if (IS_PLAYER_GUID(caster->GetCharmerGUID()))
    {
        Unit const* owner = caster->GetCharmer();
        if (owner && owner->ToPlayer()->HaveBot())
        {
            BotMap const* bmap = owner->ToPlayer()->GetBotMgr()->GetBotMap();
            for (BotMap::const_iterator itr = bmap->begin(); itr != bmap->end(); ++itr)
            {
                if (Creature const* bot = itr->second)
                {
                    bot->GetBotAI()->OnBotOwnerSpellGo(spell, ok);
                    //if (Creature const* botpet = bot->GetBotsPet())
                    //    botpet->GetBotAI()->OnBotPetOwnerSpellGo(spell, ok);
                }
            }
        }
    }
    else if (IS_CREATURE_GUID(caster->GetCharmerGUID()))
    {
        Unit const* bot = caster->GetCharmer();
        if (bot->ToCreature()->GetBotAI())
            bot->ToCreature()->GetBotAI()->OnBotSpellGo(spell, ok);
    }
}

void BotMgr::OnVehicleAttackedBy(Unit* attacker, Unit const* victim)
{
    Unit const* owner = victim->GetCharmer();
    if (IS_PLAYER_GUID(victim->GetCharmerGUID()))
        owner = victim->GetCharmer();
    else if (IS_CREATURE_GUID(victim->GetCharmerGUID()))
        if (Unit const* bot = victim->GetCharmer())
            owner = bot->ToCreature()->GetBotOwner();

    if (owner && owner->GetTypeId() == TYPEID_PLAYER && owner->ToPlayer()->HaveBot())
    {
        BotMap const* bmap = owner->ToPlayer()->GetBotMgr()->GetBotMap();
        for (BotMap::const_iterator itr = bmap->begin(); itr != bmap->end(); ++itr)
            if (Creature const* bot = itr->second)
                bot->GetBotAI()->OnOwnerVehicleDamagedBy(attacker);
    }
}

void BotMgr::OnBotDamageDealt(Unit* attacker, Unit* victim, uint32 damage, CleanDamage const* cleanDamage, DamageEffectType damagetype, SpellInfo const* spellInfo)
{
    attacker->ToCreature()->GetBotAI()->OnBotDamageDealt(victim, damage, cleanDamage, damagetype, spellInfo);
}

void BotMgr::OnBotDispelDealt(Unit* dispeller, Unit* dispelled, uint8 num)
{
    dispeller->ToCreature()->GetBotAI()->OnBotDispelDealt(dispelled, num);
}

void BotMgr::OnBotEnterVehicle(Creature const* passenger, Vehicle const* vehicle)
{
    passenger->GetBotAI()->OnBotEnterVehicle(vehicle);
}

void BotMgr::OnBotExitVehicle(Creature const* passenger, Vehicle const* vehicle)
{
    passenger->GetBotAI()->OnBotExitVehicle(vehicle);
}

void BotMgr::OnBotOwnerEnterVehicle(Player const* passenger, Vehicle const* vehicle)
{
    BotMap const* bmap = passenger->GetBotMgr()->GetBotMap();
    for (BotMap::const_iterator itr = bmap->begin(); itr != bmap->end(); ++itr)
        if (Creature const* bot = itr->second)
            if (bot->IsInWorld() && bot->IsAlive())
                bot->GetBotAI()->OnBotOwnerEnterVehicle(vehicle);
}

void BotMgr::OnBotOwnerExitVehicle(Player const* passenger, Vehicle const* vehicle)
{
    BotMap const* bmap = passenger->GetBotMgr()->GetBotMap();
    for (BotMap::const_iterator itr = bmap->begin(); itr != bmap->end(); ++itr)
        if (Creature const* bot = itr->second)
            if (bot->IsInWorld() && bot->IsAlive())
                bot->GetBotAI()->OnBotOwnerExitVehicle(vehicle);
}

void BotMgr::ApplyBotEffectMods(Unit const* caster, Unit const* target, SpellInfo const* spellInfo, uint8 effIndex, float& value)
{
    caster->ToCreature()->GetBotAI()->ApplyBotEffectMods(target, spellInfo, effIndex, value);
}

float BotMgr::GetBotDamageTakenMod(Creature const* bot, bool magic)
{
    return bot->GetBotAI()->GetBotDamageTakenMod(magic);
}

float BotMgr::GetBotDamageModPhysical()
{
    return _mult_dmg_physical;
}
float BotMgr::GetBotDamageModSpell()
{
    return _mult_dmg_spell;
}
float BotMgr::GetBotHealingMod()
{
    return _mult_healing;
}

bool HandleCreateAddNpcbotforEntry(Player * chr, uint32 id)
{
	if (!chr)
		return false;

	ChatHandler ch(chr->GetSession());

	BotMgr* mgr = chr->GetBotMgr();
	if (!mgr)
		mgr = new BotMgr(chr);

	CreatureTemplate const* creInfo = sObjectMgr->GetCreatureTemplate(id);

	if (!creInfo)
	{
		//ch.PSendSysMessage("creature %u does not exist!", id);
		ch.PSendSysMessage("生物ID为： %u 不存在!", id);
		ch.SetSentErrorMessage(true);
		return false;
	}

	if (!(creInfo->flags_extra & CREATURE_FLAG_EXTRA_NPCBOT))	//判断是不是npcbot
	{
		//ch.PSendSysMessage("creature %u is not a npcbot!", id);
		ch.PSendSysMessage("生物ID为： %u 不是佣兵!", id);
		ch.SetSentErrorMessage(true);
		return false;
	}

	if (chr->GetBotMgr()->RestrictBots(nullptr, false))
	{
		ch.PSendSysMessage("你现在所处地区是限制佣兵的，请离开此区域后再试。");
		ch.SetSentErrorMessage(true);
		return false;
	}

	if (chr->GetNpcBotsCount() >= sCharMod->GetPlayerBotNum(chr->GetSession()->GetAccountId()))
	{
		ch.PSendSysMessage("佣兵招满了！");
		return false;
	}

	if (chr->GetNpcBotsCount() >= sCharMod->GetPlayerBotNum(chr->GetSession()->GetAccountId()))
	{
		ch.PSendSysMessage("佣兵招满了！");
		return false;
	}

	if (Transport* trans = chr->GetTransport())
	{
		//ch.SendSysMessage("Cannot spawn bots on transport!");
		ch.SendSysMessage("你现在正在交通工具上，无法创建佣兵！");
		ch.SetSentErrorMessage(true);
		return false;
	}

	//Map* map = sMapMgr->CreateBaseMap(0);
	//map->LoadGrid(x, y);

	//GM岛坐标
	float x = 16222.1f;
	float y = 16252.1f;
	float z = 12.5872f;
	float o = 0.f;
	Map * map = sMapMgr->CreateBaseMap(0);
	uint32 pguid = chr->GetGUIDLow();

	if (BotDataMgr::IsHasTheNpcBot(id, pguid))
	{
		uint32 guid = BotDataMgr::GetTheNpcBotGuid(id, pguid);

		if (guid)
		{ 
			//uint32 mapId = 0;
			//float x = 16222.1f;
			//float y = 16252.1f;
			//float z = 12.5872f;
			//float o = 0.f;
			//CellCoord c = Trinity::ComputeCellCoord(x, y);
			//GridCoord g = Trinity::ComputeGridCoord(x, y);
			//ASSERT(c.IsCoordValid() && "Invalid Cell coord!");
			//ASSERT(g.IsCoordValid() && "Invalid Grid coord!");
			//Map* map = sMapMgr->CreateBaseMap(mapId);
			//if (!map->IsGridLoaded(x, y))
			//	map->LoadGrid(x, y);

			PreparedStatement* stmt = WorldDatabase.GetPreparedStatement(WORLD_SEL_CREATURE_BY_GUID);
			//"SELECT guid FROM creature WHERE guid = ?", CONNECTION_SYNCH
			stmt->setUInt32(0, guid);
			PreparedQueryResult result = WorldDatabase.Query(stmt);

			if (result)
			{
				uint64 cguid = MAKE_NEW_GUID(guid, id, HIGHGUID_UNIT);
				Unit* u = ObjectAccessor::FindUnit(cguid);
				if (u)
				{
					Creature* _bot = u->ToCreature();
					if (bot_ai* b_ai = _bot->GetBotAI())
					{
						if (b_ai->IsCheakSummon())
						{
							ch.PSendSysMessage("你的佣兵：%s 就在你身边！", _bot->GetName().c_str());
							return false;
						}
						else
						{
							if (mgr->AddBot(_bot, false) == BOT_ADD_SUCCESS)
							{
								ch.PSendSysMessage("你的佣兵：%s 已经召唤成功，请稍等！", _bot->GetName().c_str());
								return true;
							}
							else
							{
								ch.PSendSysMessage("您的佣兵在忙着摸鱼！请稍后再试！");
								sLog->outError("玩家：%s （guid：%u）使用佣兵召唤命令过于频繁，召唤佣兵entry: %u (guid: %u )", chr->GetName().c_str(), chr->GetGUIDLow(), id, guid);
								return false;
							}
						}
					}
					else
					{
						sLog->outError("玩家：%s （guid：%u）使用佣兵召唤命令出错，召唤佣兵entry: %u (guid: %u )", chr->GetName().c_str(), chr->GetGUIDLow(), id, guid);
						return false;
					}
				}
				else
				{
					Creature* creature = new Creature();
					if (!creature->Create(guid, map, chr->GetPhaseMaskForSpawn(), id, (uint32)0, x, y, z, o))
					{
						delete creature;
						ch.SendSysMessage("Creature is not created!");
						ch.SetSentErrorMessage(true);
						return false;
					}

					if (!creature->LoadBotCreatureFromDB(guid, map))
					{
						ch.SendSysMessage("Cannot load npcbot from DB!");
						ch.SetSentErrorMessage(true);
						delete creature;
						return false;
					}

					sObjectMgr->AddCreatureToGrid(guid, sObjectMgr->GetCreatureData(guid));

					//handler->SendSysMessage("NpcBot successfully spawned");
					ch.SendSysMessage("您的佣兵准备完毕，即将来到你身！！");

					if (mgr->AddBot(creature, false) == BOT_ADD_SUCCESS)
					{
						//handler->PSendSysMessage("%s is now your npcbot", creature->GetName().c_str());
						ch.PSendSysMessage("%s 已成功召唤！！", creature->GetName().c_str());
						return true;
					}
					sLog->outError("玩家：%s （guid：%u）使用佣兵召唤命令出现未知错误，召唤佣兵entry: %u (guid: %u )", chr->GetName().c_str(), chr->GetGUIDLow(), id, guid);
					ch.PSendSysMessage("出现未知错误，请联系GM！");
					return false;
				}
			}
			else
			{
				ch.PSendSysMessage("你召唤佣兵出现未知错误请联系GM！");
				sLog->outError("玩家：%s （guid：%u）使用佣兵召唤命令出错生物表中没有NPCBOT信息，召唤佣兵entry: %u (guid: %u )", chr->GetName().c_str(), chr->GetGUIDLow(), id, guid);
				return false;
			}
		}
		else
		{
			ch.PSendSysMessage("你召唤佣兵出现错误请联系GM！");
			sLog->outError("玩家：%s （guid：%u）使用佣兵召唤命令出错，召唤佣兵entry: %u 出现没有GUID的错误", chr->GetName().c_str(), chr->GetGUIDLow(), id);
			return false;
		}
	}



	//if (map->Instanceable())
	//{
	//	//ch.SendSysMessage("Cannot spawn bots in instances!");
	//	ch.SendSysMessage("你现在正在副本中，无法创建佣兵！");
	//	ch.SetSentErrorMessage(true);
	//	return false;
	//}

	Creature* creature = new Creature();
	if (!creature->Create(sObjectMgr->GenerateLowGuid(HIGHGUID_UNIT), map, chr->GetPhaseMaskForSpawn(), id, 0, x, y, z, o))
	{
		delete creature;
		//ch.SendSysMessage("Creature is not created!");
		ch.SendSysMessage("这个生物不能被创建为佣兵!");
		ch.SetSentErrorMessage(true);
		return false;
	}

	NpcBotExtras const* _botExtras = BotDataMgr::SelectNpcBotExtras(id);
	if (!_botExtras)
	{
		//ch.PSendSysMessage("No class/race data found for bot %u!", id);
		ch.PSendSysMessage("找不到ID为： %u 的佣兵的职业/种族数据！", id);
		ch.SetSentErrorMessage(true);
		return false;
	}
	creature->SaveToDB(map->GetId(), (1 << map->GetSpawnMode()), chr->GetPhaseMaskForSpawn());
	
	uint32 db_guid = creature->GetDBTableGUIDLow();
	
	BotDataMgr::AddNpcBotData(db_guid, id, bot_ai::DefaultRolesForClass(_botExtras->bclass), bot_ai::DefaultSpecForClass(_botExtras->bclass), creature->GetCreatureTemplate()->faction);

	if (!creature->LoadBotCreatureFromDB(db_guid, map))
	{
		//ch.SendSysMessage("Cannot load npcbot from DB!");
		ch.SendSysMessage("无法从数据库加载到佣兵的数据！");
		ch.SetSentErrorMessage(true);
		delete creature;
		return false;
	}

	sObjectMgr->AddCreatureToGrid(db_guid, sObjectMgr->GetCreatureData(db_guid));

	//ch.SendSysMessage("NpcBot successfully spawned");

	Player* owner = ch.GetSession()->GetPlayer();

	Creature* bot = creature;
	ch.PSendSysMessage("您的佣兵准备完毕，即将来到你身！");

	//if (!bot || !bot->IsNPCBot() || bot->GetBotAI()->GetBotOwnerGuid())
	//{
	//	//ch.SendSysMessage("You must select uncontrolled npcbot");
	//	ch.SendSysMessage("你必须选择一个没有被雇佣的佣兵！");
	//	ch.SetSentErrorMessage(true);
	//	return false;
	//}

	if (mgr->AddBot(bot, false) == BOT_ADD_SUCCESS)
	{
		//ch.PSendSysMessage("%s is now your npcbot", bot->GetName().c_str());
		ch.PSendSysMessage("你的新的佣兵：%s ，已经召唤成功！请稍等！", bot->GetName().c_str());
		return true;
	}

	//ch.SendSysMessage("NpcBot is NOT added for some reason!");
	ch.SendSysMessage("由于未知原因，佣兵没有创建成功！");
	ch.SetSentErrorMessage(true);
	return false;
}


void BotMgr::SetBotMod()
{
	for (auto itr = _bots.begin(); itr != _bots.end(); ++itr)
		if (itr->second->GetBotAI())
			itr->second->GetBotAI()->SetLoadStatMod(true);
}

void BotMgr::HandleBotExFaBao(uint32 playerguid, uint32 botclass)
{
	if (!_owner)
		return;

	uint32 pguid = _owner->GetGUIDLow();

	for (auto itr = _bots.begin(); itr != _bots.end(); ++itr)
	{
		if (Creature * bot = itr->second)	//NPCBOT法宝宕机治标
			if (!bot || !bot->IsInWorld())
				continue;

		if (bot_ai * botai = itr->second->GetBotAI())
		{
			uint32 _botclass = botai->GetBotClass();

			if (_botclass == botclass && pguid == playerguid)
			{
				botai->InitBotEx();
				break;
			}
		}
	}
}