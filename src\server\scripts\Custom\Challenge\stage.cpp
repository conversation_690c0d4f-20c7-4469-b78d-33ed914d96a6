﻿#pragma execution_character_set("utf-8")
#include "../Switch/Switch.h"
#include "../CommonFunc/CommonFunc.h"
#include "stage.h"
#include "Group.h"

std::vector<StageTemplate> StageVec;

void Stage::Load()
{
	StageVec.clear();
	uint32 count = 0;
	QueryResult result = WorldDatabase.PQuery(
		//		0			1		2				3
		"SELECT 关卡ID,召唤生物组ID,通关召唤物体ID,菜单文本 FROM _副本_闯关模式");
	if (result)
	{
		do
		{
			Field* fields = result->Fetch();
			StageTemplate Temp;
			Temp.stageRank = fields[0].GetUInt32();
			Temp.summonsGroupId = fields[1].GetUInt32();
			Temp.rewardGameobjectId = fields[2].GetUInt32();
			Temp.description = fields[3].GetString();
			StageVec.push_back(Temp);
			count++;
		} while (result->NextRow());

	}
	LoadDBLog("_副本_闯关模式", count);
}

std::string Stage::GetDescription(uint32 stage)
{
	uint32 len = StageVec.size();

	for (size_t i = 0; i < len; i++)
	{
		if (stage == StageVec[i].stageRank)
			return StageVec[i].description;
	}

	return "";
}

uint32 Stage::GetSumId(uint32 stage)
{
	uint32 len = StageVec.size();

	for (size_t i = 0; i < len; i++)
	{
		if (stage == StageVec[i].stageRank)
			return StageVec[i].summonsGroupId;
	}

	return 1;
}

uint32 Stage::GetGobId(uint32 stage)
{
	uint32 len = StageVec.size();

	for (size_t i = 0; i < len; i++)
	{
		if (stage == StageVec[i].stageRank)
			return StageVec[i].rewardGameobjectId;
	}

	return 0;
}

class StageNPC : public CreatureScript
{
public:
	StageNPC() : CreatureScript("StageNPC") { }
 
	bool OnGossipHello(Player* player, Creature* creature) override
	{
		if (!player->StageNPCGossip)
		{
			ChatHandler(player->GetSession()).PSendSysMessage("不要频繁操作！");
			return false;
		}

		player->PlayerTalkClass->ClearMenus();

		uint32 eventid = atoi(sSwitch->GetFlagByIndex(ST_STAGE, 1).c_str());

		if (eventid != 0 && !sGameEventMgr->IsActiveEvent(eventid))
		{
			ChatHandler(player->GetSession()).PSendSysMessage("当前时间不允许挑战！");
			return false;
		}

		if (creature->summonsClear && player->stagerank <= StageVec.size())
		{
			std::ostringstream oss;
			oss << "挑战等级:" << player->stagerank;
			player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, oss.str(), player->stagerank, GOSSIP_ACTION_INFO_DEF + 1);
			player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, sStage->GetDescription(player->stagerank), player->stagerank, GOSSIP_ACTION_INFO_DEF);
		}

		player->SEND_GOSSIP_MENU(DEFAULT_GOSSIP_MESSAGE, creature->GetGUID());
		player->StageNPCGossip = false;

		return true;
	}

	bool OnGossipSelect(Player* player, Creature* creature, uint32 sender, uint32 action) override
	{
		if (action == GOSSIP_ACTION_INFO_DEF + 1)
		{
			OnGossipHello(player, creature);
			return true;
		}
		//当挑战者为空时将点击对话开始挑战的玩家定为挑战者

		creature->SummonCreatureGroup(sStage->GetSumId(player->stagerank));
		creature->summonsClear = false;
		player->CLOSE_GOSSIP_MENU();
		return true;
	}

	struct StageNPCAI : public ScriptedAI
	{
		StageNPCAI(Creature* creature) : ScriptedAI(creature), Summons(me){}

		SummonList Summons;
		void JustSummoned(Creature* summon) override
		{
			summon->GetMotionMaster()->MoveRandom(5.0f);
			Summons.Summon(summon);
		}

		void Reset() override
		{
			me->summonsClear = true;
			Summons.DespawnAll();
		}

		void MoveInLineOfSight(Unit* who)
		{
		}

		void SummonedCreatureDies(Creature* summon, Unit* killer)  override
		{
			Player* killerPlayer = killer->GetCharmerOrOwnerPlayerOrPlayerItself();
			if (killerPlayer)
			{			//挑战等级从挑战者获取
				uint32 stage = killerPlayer->stagerank;
				std::ostringstream oss;
				oss << "挑战等级：" << stage;
				me->MonsterSay(oss.str().c_str(), LANG_UNIVERSAL, 0);

				Summons.Despawn(summon);

				if (Summons.empty())
				{
					uint32 gobEntry = sStage->GetGobId(stage);

					if (sObjectMgr->GetGameObjectTemplate(gobEntry))
						me->SummonGameObject(gobEntry, killer->GetPositionX(), killer->GetPositionY(), killer->GetPositionZ(), 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0);

					//挑战成功将挑战者的挑战等级增加1
					//同时如果玩家在一个队伍,且队伍中玩家都在一起那么队伍中的玩家也要把他们的挑战等级+1
					killerPlayer->stagerank++;
					if (Group* group = killerPlayer->GetGroup())
					{
						for (auto itr = group->GetFirstMember(); itr != NULL; itr = itr->next())
						{
							//跳过100码范围内的队友和自己
							Player* pMember = itr->GetSource();
							if (!pMember || pMember->GetGUID() == killerPlayer->GetGUID() || (!pMember->IsWithinDistInMap(killerPlayer, 100.0f, true)))
								continue;

							pMember->stagerank++;
						}
					}
					me->summonsClear = true;
				}
			}	
		}
	};

	CreatureAI* GetAI(Creature* creature) const override
	{
		return new StageNPCAI(creature);
	}
};

class StagePlayerScript : public PlayerScript
{
public:
	StagePlayerScript() : PlayerScript("StagePlayerScript") {}

	void OnMapChanged(Player* player) override
	{
		uint32 mapId = atoi(sSwitch->GetFlagByIndex(ST_STAGE, 2).c_str());

		if (player->GetMapId() != mapId)
			return;

		uint32 size = atoi(sSwitch->GetFlagByIndex(ST_STAGE, 4).c_str());

		if (Map* map = player->GetMap())
		{
			uint32 count = map->GetPlayersCountExceptGMs();

			if (count > size)
			{
				player->RepopAtGraveyard();
				return;
			}

			if (count == 1 && sSwitch->GetValueByIndex(ST_STAGE, 5) == 1)
			{
				uint32 npcId = atoi(sSwitch->GetFlagByIndex(ST_STAGE, 3).c_str());

				if (Creature* creature = player->FindNearestCreature(npcId, 150))
					creature->AI()->Reset();
			}
		}
	}

	void OnUpdate(Player* player, uint32 diff)
	{
		if (player->StageNPCGossipTimer > 3000)
		{
			player->StageNPCGossip = true;
			player->StageNPCGossipTimer = 0;
		}
		else
			player->StageNPCGossipTimer += diff;
	}
};

void AddSC_Stage()
{
	new StageNPC();
	new StagePlayerScript();
}