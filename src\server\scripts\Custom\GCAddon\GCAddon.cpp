﻿#pragma execution_character_set("utf-8")
#include "GCAddon.h"
#include "../ItemMod/ItemMod.h"
#include "../Requirement/Requirement.h"
#include "../Reward/Reward.h"
#include "../DataLoader/DataLoader.h"
#include "../CommonFunc/CommonFunc.h"
#include "Object.h"
#include "../VIP/VIP.h"
#include "../HonorRank/HonorRank.h"
#include "../ExtraEquipment/ExtraEquipment.h"
#include "../String/String.h"
#include "../Switch/Switch.h"
#include "../Transmogrification/Transmogrification.h"
#include "../Armory/Armory.h"
#include "../SignIn/SignIn.h"
#include "../Talisman/Talisman.h"
#include "../Rank/Rank.h"
#include "../SpiritPower/SpiritPower.h"
#include "../LuckDraw/LuckDraw.h"
#include "../Recovery/Recovery.h"
#include "../Market/Market.h"
#include "../AntiFarm/AntiFarm.h"
#include "../Faction/Faction.h"
#include "../Reincarnation/Reincarnation.h"
#include "../UI/BlackMarket/BlackMarket.h"
#include "../GS/GS.h"
#include "../ItemMod/CusItem.h"
#include "../BossRanking/BossRanking.h"
#include "../_SM_StatPoints/_SM_StatPoints.h"
#include "../_SM_DoubleArmor/DoubleArmor.h"
#include "../ItemShopMgr/ItemShopMgr.h"
#include "../AuctionHouse/AuctionHouseMgr.h"
#include "../CustomEvent/Event.h"
#include "../SoulStone/SoulStone.h"
#include <cctype>
#include "../SoulStoneEx/SoulStoneEx.h"
#include "../UnitMod/CharMod/CharMod.h"
#include "../ItemMod/Itemdevour.h"
#include "../Instance/InstanceRankings.h"
#include "NpcBots/bot_expend.h"

std::string GCAddon::SplitStr(std::string msg, uint32 index)
{
	std::string::size_type idx = msg.find(" ");

	if (idx != std::string::npos)
	{
		std::vector<std::string> vec = sCF->SplitStr(msg, " ");

		if (index >= vec.size())
			index = 0;

		return vec[index];
	}
	else
		return msg;
}

bool IsOpcode(std::string opcode, std::string _opcode)
{
	return strcmp(opcode.c_str(), _opcode.c_str()) == 0;
}

bool GCAddon::OnRecv(Player* player, std::string msg)
{
	stripLineInvisibleChars(msg);

	std::string opcode = SplitStr(msg, 0);

	if (IsOpcode(opcode, "GC_C_LUCKDRAW_V3"))
	{
		uint32 action = atoi(SplitStr(msg, 1).c_str());

		if (action != 1 && action != 10)
			return true;

		uint32 reqId = 0;

		if (action == 1)
			reqId = atoi(sSwitch->GetFlagByIndex(ST_LUCKDRAW, 1).c_str());
		else
			reqId = atoi(sSwitch->GetFlagByIndex(ST_LUCKDRAW, 2).c_str());

		if (action == 1)
		{
			if (!sReq->Check(player, reqId))
				return true;

			if (player->LuckDrawTotalCount == 0)
			{
				player->LuckDrawTotalCount = 1;
				player->LuckDrawCount = 1;
				player->LuckDrawTimer = 0;
			}
			else
				return true;

			sReq->Des(player, reqId);
		}
		else if (action == 10)
		{
			if (!sReq->Check(player, reqId))
				return true;

			if (player->LuckDrawTotalCount == 0)
			{
				player->LuckDrawTotalCount = 10;
				player->LuckDrawCount = 10;
				player->LuckDrawTimer = 0;
			}
			else
				return true;

			sReq->Des(player, reqId);
		}

		SendPacketTo(player, "GC_S_LUCKDRAW_V3", "START");

		return true;
	}
	else if (IsOpcode(opcode, "GC_C_RECOVERY"))
	{
		uint32 action = atoi(SplitStr(msg, 1).c_str());
		uint32 id = atoi(SplitStr(msg, 2).c_str());

		if (action == 0)
			sRecovery->SendCategoryMsg(player, id);
		else if (action == 1)
			sRecovery->Action(player, id);
		return true;
	}
	else if (IsOpcode(opcode, "GC_C_STATPOINTS"))
	{
		uint32 action = atoi(SplitStr(msg, 1).c_str());
		uint32 id = atoi(SplitStr(msg, 2).c_str());

		//增加属性点
		if (action == 0)
			sStatPoints->Ins(player, id);
		//减少属性点
		else if (action == 1)
			sStatPoints->Des(player, id);
		//全部重置
		else if (action == 2)
			sStatPoints->AllReset(player);
		//单属性重置
		else if (action == 3)
			sStatPoints->UniReset(player, id);
		return true;
	}
	else if (IsOpcode(opcode, "GC_C_ANTIFARM"))
	{
		if (player->AntiFarmCount == 0)
		{
			sAntiFarm->Action(player, AF_CHECK_KILL);
			return true;
		}
		uint32 num = atoi(SplitStr(msg, 1).c_str());
		sAntiFarm->DoCheck(player, num);
		return true;
	}
	else if (IsOpcode(opcode, "GC_C_RELOAD"))
	{
		SendAllData(player);
		return true;
	}
	else if (IsOpcode(opcode, "GC_C_CUS_ITEM"))
	{
		auto itr = CusItemMap.find(atol(SplitStr(msg, 1).c_str()));
		if (itr != CusItemMap.end())
		{
			SendItemCusData(player, itr->second);
		}
		return true;
	}
	else if (IsOpcode(opcode, "GC_C_ITEM_ENCHANTS"))
	{
		auto itr = CusItemMap.find(atol(SplitStr(msg, 1).c_str()));
		if (itr == CusItemMap.end())
		{
			uint64 guid = MAKE_NEW_GUID(atol(SplitStr(msg, 1).c_str()), 0, HIGHGUID_ITEM);
			Item* item = NULL;
			if (sAuctionMgr->IsAuctionItem(guid))
				item = sAuctionMgr->GetAItem(guid);
			else
				item = player->GetItemByGuid(guid);

			if (item)
			{
				//bool send = false;
				//for (auto itr : CreateEnchantVec)
				//{
				//	if (itr.entry == item->GetEntry())
				//	{
				//		send = true;
				//		break;
				//	}
				//}
				//if (send)	//在这发?
					SendItemEnchantInfo(item, player);
			}
		}
	}
	else if (IsOpcode(opcode, "GC_C_TALISMAN"))
	{
		uint32 action = atoi(SplitStr(msg, 1).c_str());

		if (action == 0)
		{
			uint32 ID = atoi(SplitStr(msg, 2).c_str());
			uint32 entry = atoi(SplitStr(msg, 3).c_str());
			sTalisman->EquipTalisman(player, ID, entry);
		}
		else if (action == 1)
			sTalisman->SendPacket(player);
	}
	else if (IsOpcode(opcode, "GC_C_ITEM_ENTRY"))
	{
		SendItemData(player, atoi(SplitStr(msg, 1).c_str()));
		return true;
	}
	
	else if (IsOpcode(opcode, "CLIENT_BOSSRANK_UICLOSE"))
	{
		if (!sBR->IsInRank(player->GetGUIDLow()))
			return false;

		if (sBR->IsStart())
			sBR->SetSend(player->GetGUIDLow(), false);

	}
	else if (IsOpcode(opcode, "CLIENT_BOSSRANK_REQDATA"))
	{
		sGCAddon->SendPacketTo(player, "SERVER_RANK_MINDAMAGE", std::to_string(100));

		if (sBR->IsStart())
		{
			if (!sBR->IsInRank(player->GetGUIDLow()))
				return false;

			sBR->SetSend(player->GetGUIDLow(), false);
			SendPacketTo(player, "SERVER_RANK_START", "OPEN_UI");
		}
		else
		{
			SendPacketTo(player, "SERVER_RANK_END", "CLOSE_UI");
		}
	}

	////////////////////////      UI界面新双甲功能脚本 ////////////////////////////
	else if (IsOpcode(opcode, "SM_DoubleArmor_LOAD"))
	{

	}
	//点击初始按钮显示物品信息【空纹理】和【有数据纹理】操作码
	else if (IsOpcode(opcode, "DoubleArmorItemID"))
	{
		uint32 SlotID = atoi(SplitStr(msg, 1).c_str());                                     //位置ID
		//sLog->outString("slotid = %u",SlotID);
		sDoubleArmor->GetSlotText(player, EquipmentSlots(SlotID));
		//sDoubleArmor->SendDoubleArmorStat(player);
	}
	//左键点击装备栏按钮操作码
	else if (IsOpcode(opcode, "DOUBLE_ARMOR_LEFTBUTTON_CLICK"))
	{
		uint32 SlotID = atoi(SplitStr(msg, 1).c_str());                                     //位置ID
		uint32 ItemID = atoi(SplitStr(msg, 2).c_str());                                     //物品ID
		Item* item = player->GetItemByEntry(ItemID);
		sDoubleArmor->EquipDoubleArmor(player, SlotID, item, true, ItemID);
	}
	//右键点击取下装备栏按钮操作码
	else if (IsOpcode(opcode, "DOUBLE_ARMOR_RIGHTBUTTON_CLICK"))
	{
		uint32 SlotID = atoi(SplitStr(msg, 1).c_str());                                     //按钮物品框ID
		uint32 ItemID = atoi(SplitStr(msg, 2).c_str());

		//if (player->ToPlayer()->GetFreeInventorySpace() == 0)
		//{
		//	player->GetSession()->SendNotification("无法取下物品，背包没有多余的空间");
		//	return NULL;
		//}
		sDoubleArmor->RemoveDoubleArmorItem(player, SlotID, ItemID);
	}

	//////////////////////////////商城系统//////////////////////////////
	else if (IsOpcode(opcode, "GetShopTabName"))
	{
		sItemShopMgr->SendTabNames(player);
		//sLog->outString("获取标签");
	}
	else if (IsOpcode(opcode, "GetShopTabPage"))
	{
		Tokenizer Datas(opcode, ' ');
		uint32 SelectTab = atoi(SplitStr(msg, 1).c_str());
		uint32 SelectPage = atoi(SplitStr(msg, 2).c_str());
		sItemShopMgr->SendDataForPage(player, SelectTab, SelectPage);
		//sLog->outString("获取页面");
	}
	else if (IsOpcode(opcode, "RequestBuyNeedInfo"))
	{
		uint32 ItemId = atoi(SplitStr(msg, 1).c_str());
		uint32 GoType = atoi(SplitStr(msg, 2).c_str());
		uint32 SelectTab = atoi(SplitStr(msg, 3).c_str());
		sItemShopMgr->SendNTempGtsForPage(player, ItemId, GoType, SelectTab);
		//sLog->outString("购买信息");
	}
	else if (IsOpcode(opcode, "AcceptBuyShopItem"))
	{
		//sLog->outString("点击购买");
		uint32 ItemId = atoi(SplitStr(msg, 1).c_str());
		uint32 GoType = atoi(SplitStr(msg, 2).c_str());
		uint32 SelectTab = atoi(SplitStr(msg, 3).c_str());
		int32 NTempId = sItemShopMgr->GetReqId(ItemId, SelectTab);
		if (NTempId < 0)
			sLog->outError("账号ID：%u, 玩家：%s 疑似想白嫖商城物品：%u ", player->GetSession()->GetAccountId(), player->GetName().c_str(), ItemId);
		
		if (GoType == 1)
		{
			if (!sItemShopMgr->IsBuyItem(player, ItemId))
				return false;
			if (!sReq->Check(player, NTempId))
				return false;

			auto itemmaxbuymap = sDataLoader->GetItemMaxBuyDataMap();
			auto itr = itemmaxbuymap->find(ItemId);
			if (itr != itemmaxbuymap->end())
			{
				auto pite = player->m_AItemBuyCountDataMap.find(ItemId);
				if (pite != player->m_AItemBuyCountDataMap.end())
				{
					if (pite->second.itemid == ItemId)
					{
						if (pite->second.count >= itr->second.amaxcount || pite->second.count + 1 > itr->second.amaxcount)
						{
							ChatHandler(player->GetSession()).PSendSysMessage("账号已经超过最大购买上限");
							return false;
						}
					}
				}

				auto pitr = player->m_PItemBuyCountDataMap.find(ItemId);
				if (pitr != player->m_PItemBuyCountDataMap.end())
				{
					if (pitr->second.itemid == ItemId)
					{
						if (pitr->second.count >= itr->second.maxcount || pitr->second.count + 1 > itr->second.maxcount)
						{
							ChatHandler(player->GetSession()).PSendSysMessage("已经超过最大购买上限！");
							return false;
						}
					}
				}
			}

			if (player->DayLimit(ItemId, 1))
				return false;

			uint32  desTokenCount = 0;
			uint32  discount = sItemShopMgr->GetDiscount(player, ItemId, SelectTab);
			if (discount > 0)
			{
				UNORDERED_MAP<uint32, ReqTemplate>::iterator iter = ReqMap.find(NTempId);
				if (iter != ReqMap.end())
					desTokenCount = (discount * iter->second.desTokenCount) / 100;
				if (desTokenCount > 0) sCF->UpdateTokenAmount(player, desTokenCount, false, "[REQ]扣除");
			}
			else
			{
				sReq->Des(player, NTempId);
			}

			player->AddItem(ItemId, 1);
		}
		else if (GoType == 2)
		{
			if (!sItemShopMgr->IsCanLearn(player, ItemId, GoType))
				return false;
			if (!sReq->Check(player, NTempId))
				return false;
			sReq->Des(player, NTempId);
			player->learnSpell(ItemId);
		}
	}

	//////////////////////////////AA弹窗系统//////////////////////////////
	else if (IsOpcode(opcode, "GC_C_EVENT_ACCEPT"))
	{
		sEvent->Dual_Event_CallBack(player, atoi(SplitStr(msg, 1).c_str()), atoi(SplitStr(msg, 2).c_str()), 1);
	}
	else if (IsOpcode(opcode, "GC_C_EVENT_CANCEL"))
	{
		sEvent->Dual_Event_CallBack(player, atoi(SplitStr(msg, 1).c_str()), atoi(SplitStr(msg, 2).c_str()), 2);
	}

	//////////////////////////////魂玉系统//////////////////////////////
	else if (IsOpcode(opcode, "SSC_ITEM_TO_SLOT"))
	{
		std::string str = SplitStr(msg, 1);
		std::vector<std::string> vec = sSoulStone->split(str, "#");

		if (vec.size() != 3)
			return false;

		for (std::vector<std::string>::iterator itr = vec.begin(); itr != vec.end(); ++itr)
			if (!isNumber(*itr))
				return false;

		uint32 page = atoi(vec[0].c_str());
		uint32 slot = atoi(vec[1].c_str());
		uint32 item = atoi(vec[2].c_str());
		if (!sSoulStone->IsUpSSLimit(page, slot))
			return false;

		sSoulStone->CanInSetToSlot(player, page, slot, item, str);
	}
	else if (IsOpcode(opcode, "SSC_REMOVE_SLOT_ITEM"))
	{
		std::string str = SplitStr(msg, 1);
		std::vector<std::string> vec = sSoulStone->split(str, "#");

		if (vec.size() != 2)
			return false;

		for (std::vector<std::string>::iterator itr = vec.begin(); itr != vec.end(); ++itr)
			if (!isNumber(*itr))
				return false;

		uint32 page = atoi(vec[0].c_str());
		uint32 slot = atoi(vec[1].c_str());

		if (!sSoulStone->IsUpSSLimit(page, slot))
			return false;

		sSoulStone->CanRemoveSlot(player, page, slot, str);
	}
	else if (IsOpcode(opcode, "SSC_LIMIT"))
	{
		if (SplitStr(msg, 1) == "VAL")
		{
			sSoulStone->SendMutualData(player);
		}
	}
	else if (IsOpcode(opcode, "SSC_ACTI"))
	{
		if (SplitStr(msg, 1) == "ISOK")
		{
			sSoulStone->SendAllActiData(player);
		}
	}
	else if (IsOpcode(opcode, "SSC_BUY_PAGE"))
	{
		std::string str = SplitStr(msg, 1);

		if (!isNumber(str))
			return false;

		uint32 page = atoi(str.c_str());

		if (!sSoulStone->IsBuyPageReq(player, page))
		{
			return false;
		}
	}

	//////////////////////////////魂石系统//////////////////////////////
	else if (IsOpcode(opcode, "SSCEX_ITEM_TO_SLOT"))
	{
		std::string str = SplitStr(msg, 1);
		std::vector<std::string> vec = sSoulStoneEx->split(str, "#");

		if (vec.size() != 3)
			return false;

		for (std::vector<std::string>::iterator itr = vec.begin(); itr != vec.end(); ++itr)
			if (!isNumber(*itr))
				return false;

		uint32 page = atoi(vec[0].c_str());
		uint32 slot = atoi(vec[1].c_str());
		uint32 item = atoi(vec[2].c_str());

		if (!sSoulStoneEx->IsUpSSExLimit(page, slot))
			return false;

		sSoulStoneEx->CanInSetToSlot(player, page, slot, item, str);
	}
	else if (IsOpcode(opcode, "SSCEX_REMOVE_SLOT_ITEM"))
	{
		std::string str = SplitStr(msg, 1);
		std::vector<std::string> vec = sSoulStoneEx->split(str, "#");

		if (vec.size() != 2)
			return false;

		for (std::vector<std::string>::iterator itr = vec.begin(); itr != vec.end(); ++itr)
			if (!isNumber(*itr))
				return false;

		uint32 page = atoi(vec[0].c_str());
		uint32 slot = atoi(vec[1].c_str());

		if (!sSoulStoneEx->IsUpSSExLimit(page, slot))
			return false;

		sSoulStoneEx->CanRemoveSlot(player, page, slot, str);
	}
	else if (IsOpcode(opcode, "SSCEX_LIMIT"))
	{
		if (SplitStr(msg, 1) == "VAL")
		{
			sSoulStoneEx->SendMutualData(player);
		}
	}
	else if (IsOpcode(opcode, "SSCEX_ACTI"))
	{
		if (SplitStr(msg, 1) == "ISOK")
		{
			sSoulStoneEx->SendAllActiData(player);
		}
	}
	else if (IsOpcode(opcode, "SSCEX_BUY_PAGE"))
	{
		std::string str = SplitStr(msg, 1);

		if (!isNumber(str))
			return false;

		uint32 page = atoi(str.c_str());

		if (!sSoulStoneEx->IsBuyPageReq(player, page))
		{
			return false;
		}
	}
	////////////////////////    自定义复活功能  ////////////////////////////
	else if (IsOpcode(opcode, "CSREX"))
	{
		if (SplitStr(msg, 1) == "REQ")
		{
			sCharMod->HandleRex(player);
		}

		if (SplitStr(msg, 1) == "DEAD")
		{
			if (sCharMod->CheckRex(player))
				SendPacketTo(player, "SSREX_FRAME", "SHOW");
		}
	}
	////////////////////////    吞噬功能  ////////////////////////////
	else if (IsOpcode(opcode, "CSDEVOUR"))
	{
		std::string str = SplitStr(msg, 1);
		std::vector<std::string> vec = sSoulStoneEx->split(str, "#");

		if (vec.size() != 2)
			return false;

		for (std::vector<std::string>::iterator itr = vec.begin(); itr != vec.end(); ++itr)
			if (!isNumber(*itr))
				return false;

		uint32 itemguid = atoi(vec[0].c_str());
		uint32 itemid = atoi(vec[1].c_str());

		sItemDevour->SendItemDevourCount(player, itemguid, itemid);
	}
	else if (IsOpcode(opcode, "AUCTIONITEMS"))
	{
		auto itr = CusItemMap.find(atol(SplitStr(msg, 1).c_str()));
		if (itr != CusItemMap.end())
		{
			SendItemCusData(player, itr->second, true);
		}
		return true;
	}
	////////////////////////    副本BOSS竞速排行榜  ////////////////////////////
	else if (IsOpcode(opcode, "KILLBOSSRANK"))
	{
		sInstanceRanks->SendKillBossTempAllMapIdData(player);
		sInstanceRanks->SendKillBossTemplateData(player);
		sInstanceRanks->SendAllKillBossTimeData(player);
		sInstanceRanks->SendPlayerOwnerKillBossTimeAllData(player);
		return true;
	}
	else if (IsOpcode(opcode, "OPENLUCKDRAW"))
	{
		sLuckDraw->OpenPanel(player);
		return true;
	}
	else if (IsOpcode(opcode, "OPENSTATPOINT"))
	{
		sStatPoints->OpenPanel(player);
		return true;
	}
	else if (IsOpcode(opcode, "OPENTALISMAN"))
	{
		sTalisman->SendPacket(player);
		return true;
	}
	else if (IsOpcode(opcode, "BOT_FABAO_READY"))
	{
		sbotexp->SendAllNpcBotFaBaoData(player);
		return true;
	}
	else if (IsOpcode(opcode, "GC_C_BOTFABAO_XQ"))
	{
		std::string classIdstr = SplitStr(msg, 1);
		std::string slotIdstr = SplitStr(msg, 2);
		std::string itemidstr = SplitStr(msg, 3);

		//sLog->outString("msg = %s | classIdstr = %s | slotIdstr = %s | itemidstr = %s",msg.c_str(), classIdstr.c_str(), slotIdstr.c_str(), itemidstr.c_str());

		uint32 botclassId = 0;
		uint32 slotId = 0;
		uint32 itemid = 0;

		if (!SafeStringToInt(classIdstr, botclassId) || !SafeStringToInt(slotIdstr, slotId) || !SafeStringToInt(itemidstr, itemid))
			return false;

		if (!sbotexp->CanEquipFaBao(itemid, botclassId, slotId))
			return false;

		sbotexp->EquipFaBao(player, botclassId, slotId, itemid);

		return true;
	}
	else if (IsOpcode(opcode, "GC_C_BOTFABAO_XX"))
	{
		std::string classIdstr = SplitStr(msg, 1);
		std::string slotIdstr = SplitStr(msg, 2);

		uint32 botclassId = 0;
		uint32 slotId = 0;
		
		//sLog->outString("msg = %s | classIdstr = %s | slotIdstr = %s ",msg.c_str(), classIdstr.c_str(), slotIdstr.c_str());
		
		if (!SafeStringToInt(classIdstr, botclassId) || !SafeStringToInt(slotIdstr, slotId))
			return false;

		sbotexp->UnEquipFaBao(player, botclassId, slotId);

		return true;
	}
	return false;
}

void GCAddon::SendCharData(Player* player)
{
	std::ostringstream oss;
	oss << player->vipLevel << " ";
	oss << player->hrLevel << " ";
	oss << player->faction << " ";
	oss << player->reincarnationLv << " ";
	oss << player->rankLevel << " ";
	oss << player->rankValue << " ";
	oss << player->maxRankValue << " ";
	oss << player->TalismanValue << " ";
	SendPacketTo(player, "GC_S_CHAR", oss.str());
}

void GCAddon::SendAllData(Player* player)
{
	SendReqData(player);
	SendRewData(player);
	//char data
	SendCharData(player);
	//token
	{
		std::ostringstream oss;
		oss << sCF->GetTokenAmount(player);
		SendPacketTo(player, "GC_S_TOKEN", oss.str());
	}
	//daylimititem
	for (auto itr = player->PDayLimitItemMap.begin(); itr != player->PDayLimitItemMap.end(); itr++)
	{
		std::ostringstream oss;
		oss << itr->first << " ";
		oss << itr->second;
		SendPacketTo(player, "GC_S_ITEM_DAYLIMIT", oss.str());
	}
	//other
	{
		std::ostringstream oss;
		oss << atoi(sSwitch->GetFlagByIndex(ST_LUCKDRAW, 1).c_str()) << " ";
		oss << atoi(sSwitch->GetFlagByIndex(ST_LUCKDRAW, 2).c_str());
		SendPacketTo(player, "GC_S_OTHER_DATA", oss.str());
	}
	//qrencode
	{
		std::ostringstream oss;
		oss << sSwitch->GetFlagByIndex(ST_QRENCODE, 1) << " ";
		oss << sSwitch->GetFlagByIndex(ST_QRENCODE, 2) << " ";
		oss << sSwitch->GetFlagByIndex(ST_QRENCODE, 3);
		SendPacketTo(player, "GC_S_QRENCODE", oss.str());
	}
	//vip
	for (auto iter = VIPVec.begin(); iter != VIPVec.end(); ++iter)
	{
		std::ostringstream oss;
		oss << iter->vipLv << " ";
		oss << iter->icon << " ";
		oss << iter->name << " ";
		oss << iter->reqId << " ";
		oss << iter->rewId;
		SendPacketTo(player, "GC_S_VIP", oss.str());
	}
	//hr
	for (auto iter = HRUpVec.begin(); iter != HRUpVec.end(); ++iter)
	{
		std::ostringstream oss;
		oss << iter->teamId << " ";
		oss << iter->level << " ";
		oss << iter->icon << " ";
		oss << iter->name << " ";
		oss << iter->reqId << " ";
		oss << iter->rewId;
		SendPacketTo(player, "GC_S_HR", oss.str());
	}
	//faction
	{
		std::ostringstream oss;
		for (auto iter = FactionDataMap.begin(); iter != FactionDataMap.end(); ++iter)
			oss << iter->first << "-" << iter->second.name << " ";
		SendPacketTo(player, "GC_S_FACTION", oss.str());
	}
	//Reincarnation
	for (auto iter = ReincarnationMap.begin(); iter != ReincarnationMap.end(); ++iter)
	{
		std::ostringstream oss;
		oss << iter->first << "^" << iter->second.gossipText;
		SendPacketTo(player, "GC_S_REINCARNATION", oss.str());
	}
	//rank
	for (auto iter = RankDataMap.begin(); iter != RankDataMap.end(); ++iter)
	{
		std::ostringstream oss;
		oss << iter->first << "^" << iter->second.name;
		SendPacketTo(player, "GC_S_RANK", oss.str());
	}
	//luckdraw
	{
		std::ostringstream oss;
		for (auto iter = LuckDrawMap.begin(); iter != LuckDrawMap.end(); iter++)
		{
			if (ItemTemplate const* pProto = sObjectMgr->GetItemTemplate(iter->first))
				player->GetSession()->SendPacket(&pProto->queryData);
			oss << iter->first << "-" << iter->second.count << " ";
		}
		SendPacketTo(player, "GC_S_LUCKDRAW", oss.str());
	}//cusitem
	{
		//主背包
		for (uint8 i = INVENTORY_SLOT_ITEM_START; i < INVENTORY_SLOT_ITEM_END; i++)
			if (Item* item = player->GetItemByPos(INVENTORY_SLOT_BAG_0, i))
				if (item->GetUInt32Value(ITEM_FIELD_PROPERTY_SEED) == item->GetGUIDLow() + CUS_ITEM_ENTRY)
					SendItemCusData(item->GetOwner(), item->_cus_proto);

		//额外三个背包
		for (uint8 i = INVENTORY_SLOT_BAG_START; i < INVENTORY_SLOT_BAG_END; i++)
			if (Bag* pBag = player->GetBagByPos(i))
				for (uint32 j = 0; j < pBag->GetBagSize(); j++)
					if (Item* item = player->GetItemByPos(i, j))
						if (item->GetUInt32Value(ITEM_FIELD_PROPERTY_SEED) == item->GetGUIDLow() + CUS_ITEM_ENTRY)
							SendItemCusData(item->GetOwner(), item->_cus_proto);

		//银行
		for (uint8 i = BANK_SLOT_ITEM_START; i < BANK_SLOT_BAG_END; ++i)
			if (Item* item = player->GetItemByPos(INVENTORY_SLOT_BAG_0, i))
				if (item->GetUInt32Value(ITEM_FIELD_PROPERTY_SEED) == item->GetGUIDLow() + CUS_ITEM_ENTRY)
					SendItemCusData(item->GetOwner(), item->_cus_proto);
		//银行背包
		for (uint8 i = BANK_SLOT_BAG_START; i < BANK_SLOT_BAG_END; ++i)
			if (Bag* pBag = player->GetBagByPos(i))
				for (uint32 j = 0; j < pBag->GetBagSize(); j++)
					if (Item* item = player->GetItemByPos(i, j))
						if (item->GetUInt32Value(ITEM_FIELD_PROPERTY_SEED) == item->GetGUIDLow() + CUS_ITEM_ENTRY)
							SendItemCusData(item->GetOwner(), item->_cus_proto);
	}
	SendAuctionItemCusData(player);
}

void GCAddon::SendReqData(Player* player)
{
	for (auto iter = ReqMap.begin(); iter != ReqMap.end(); iter++)
	{
		std::ostringstream oss;

		oss << iter->first << "#";
		oss << iter->second.meetLevel << "#";
		oss << iter->second.meetVipLevel << "#";
		oss << iter->second.meetHRRank << "#";
		oss << iter->second.meetFaction << "#";
		oss << iter->second.meetRankLevel << "#";
		oss << iter->second.reincarnation << "#";
		oss << iter->second.meetAchievementPoints << "#";
		oss << iter->second.desGoldCount << "#";
		oss << iter->second.desTokenCount << "#";
		oss << iter->second.desXp << "#";
		oss << iter->second.desHRPoints << "#";
		oss << iter->second.desArenaPoints << "#";
		oss << iter->second.desSpiritPower << "#";

		for (size_t i = 0; i < 10; i++)
		{
			uint32 entry = iter->second.desItem[i];
			uint32 count = iter->second.desItemCount[i];

			oss << entry << "#";
			oss << count << "#";
		}

		if (iter->second.MapDataVec.empty())
			oss << "0";
		else
		{
			for (auto i = iter->second.MapDataVec.begin(); i != iter->second.MapDataVec.end(); i++)
			{
				std::string name = GetMapNameById(i->map);
				std::string zone = GetZoneNameById(i->zone);
				if (!zone.empty())
					name += "/" + zone;
				std::string area = GetAreaNameById(i->area);
				if (!zone.empty())
					name += "/" + area;
				oss << name << "~";
			}
		}
		oss << "#";
		
		if (iter->second.SpellDataVec.empty())
			oss << "0";
		else
		{
			for (auto i = iter->second.SpellDataVec.begin(); i != iter->second.SpellDataVec.end(); i++)
			{
				int32 spell = *i;

				if (SpellInfo const*  spellInfo = sSpellMgr->GetSpellInfo(abs(spell)))
				{
					if (spell > 0)
						oss << 1 << "*" << std::string(spellInfo->SpellName[4]) << "~";
					else
						oss << -1 << "*" << std::string(spellInfo->SpellName[4]) << "~";
				}
					
			}
		}
		oss << "#";

		if (iter->second.QuestDataVec.empty())
			oss << "0";
		else
		{
			for (auto i = iter->second.QuestDataVec.begin(); i != iter->second.QuestDataVec.end(); i++)
			{
				int32 quest = *i;

				if (Quest const* questProto = sObjectMgr->GetQuestTemplate(abs(quest)))
				{
					if (quest > 0)
						oss << 1 << "*" << questProto->GetTitle() << "~";
					else
						oss << -1 << "*" << questProto->GetTitle() << "~";
				}
					
			}
		}
		oss << "#";

		if (iter->second.AchieveDataVec.empty())
			oss << "0";
		else
		{
			for (auto i = iter->second.AchieveDataVec.begin(); i != iter->second.AchieveDataVec.end(); i++)
			{
				uint32 entryId = *i;
				if (AchievementEntry const* achieve = sAchievementStore.LookupEntry(entryId))
					oss << std::string(achieve->name[4]) << ",";
			}
		}

		oss << "#";
		//command
		uint32 count = 0;
		for (auto itr = iter->second.CommandDataVec.begin(); itr != iter->second.CommandDataVec.end(); itr++)
		{
			if (!itr->command.empty())
			{
				oss << itr->des << "#";
				oss << itr->icon << "#";
				count++;
			}
		}
		for (size_t i = count; i < 10; i++)
		{
			oss << 0 << "#";
			oss << 0 << "#";
		}

		SendPacketTo(player, "GC_S_REQ", oss.str());
	}
}

void GCAddon::SendRewData(Player* player)
{
	for (auto iter = RewMap.begin(); iter != RewMap.end(); iter++)
	{
		std::ostringstream oss;

		oss << iter->first << "#";
		oss << iter->second.goldCount << "#";
		oss << iter->second.tokenCount << "#";
		oss << iter->second.xp << "#";
		oss << iter->second.statPoints << "#";
		oss << iter->second.hrPoints << "#"; 
		oss << iter->second.arenaPoints << "#";

		//item
		uint32 count = 0;
		for (auto itr = iter->second.ItemDataVec.begin(); itr != iter->second.ItemDataVec.end(); itr++)
		{
			if (count > 10)
				continue;

			oss << itr->itemId << "#";
			oss << itr->itemCount << "#";
			count++;
		}
		for (size_t i = count; i < 10; i++)
		{
			oss << 0 << "#";
			oss << 0 << "#";
		}
			
		//spell
		count = 0;
		for (auto itr = iter->second.SpellDataVec.begin(); itr != iter->second.SpellDataVec.end(); itr++)
		{
			if (*itr < 0 || count > 10)
				continue;

			oss << *itr << "#";
			count++;
		}
		for (size_t i = count; i < 10; i++)
			oss << 0 << "#";

		//aura
		count = 0;
		for (auto itr = iter->second.SpellDataVec.begin(); itr != iter->second.SpellDataVec.end(); itr++)
		{
			if (*itr > 0 || count > 10)
				continue;

			oss << abs(*itr) << "#";
			count++;
		}
		for (size_t i = count; i < 10; i++)
			oss << 0 << "#";

		//command
		count = 0;
		for (auto itr = iter->second.CommandDataVec.begin(); itr != iter->second.CommandDataVec.end(); itr++)
		{
			if (!itr->command.empty())
			{
				oss << itr->des << "#";
				oss << itr->icon << "#";
				count++;
			}
		}
		for (size_t i = count; i < 10; i++)
		{
			oss << 0 << "#";
			oss << 0 << "#";
		}
			
		SendPacketTo(player, "GC_S_REW", oss.str());
	}
}

void GCAddon::SendItemData(Player* player, uint32 entry)
{
	auto iter = UIItemEntryMap.find(entry);

	if (iter != UIItemEntryMap.end())
	{
		std::ostringstream oss;
		oss << iter->first << "^";
		oss << iter->second.des << "^";
		oss << iter->second.heroText << "^";
		oss << iter->second.daylimit << "^";
		oss << iter->second.exchange1 << "^";
		oss << iter->second.exchangeReqId1 << "^";
		oss << iter->second.exchange2 << "^";
		oss << iter->second.exchangeReqId2 << "^";
		oss << iter->second.unbindReqId << "^";
		oss << iter->second.useReqId << "^";
		oss << iter->second.equipReqId << "^";
		oss << iter->second.buyReqId << "^";
		oss << iter->second.sellRewId << "^";
		oss << iter->second.recoveryRewId << "^";
		SendPacketTo(player, "GC_S_ITEM_ENTRY", oss.str());
	}
}

void GCAddon::SendItemEnchantInfo(Item* item, Player* pl)
{
	if (item->GetUInt32Value(ITEM_FIELD_PROPERTY_SEED) == 0)
		item->SetUInt32Value(ITEM_FIELD_PROPERTY_SEED, item->GetGUIDLow());
	std::ostringstream ss;
	ss << item->GetGUIDLow() << "~";
	if (pl)
	{
		for (size_t i = PERM_ENCHANTMENT_SLOT; i < MAX_ENCHANTMENT_SLOT; i++)
			if (SpellItemEnchantmentEntry const* pEnchant = sSpellItemEnchantmentStore.LookupEntry(item->GetEnchantmentId(EnchantmentSlot(i))))
				ss << i<<"#"<<pEnchant->description[4] << "@";
		SendPacketTo(pl, "GC_S_ITEM_ENCHANTS", ss.str());
	}
}

void GCAddon::SendDualEventData(Player* player, uint32 eventid)
{
	auto conf = DualEventContainer.find(eventid);
	time_t timep;
	time(&timep); /*当前time_t类型UTC时间*/
	std::ostringstream oss;
	if (conf->second.title != "" && conf->second.text != "") {
		//{id,time,"title","text"}
		oss << "{";
		oss << std::to_string(eventid) << ",";
		oss << std::to_string(timep + player->GetGUIDLow()) << ",\"";
		oss << conf->second.title << "\"," << "\"";
		oss << conf->second.text << "\"}";

		SendPacketTo(player, "GC_S_DUALEVENT", oss.str());
	}
	//sEvent->dual_event_stats[conf.id][timep + player->GetGUIDLow()] = 1;
}

bool GCAddon::isNumber(const std::string& msg)
{
	for (char ch : msg)
		if (!std::isdigit(ch))
			return false;

	return true;
}

bool GCAddon::IsPositiveInteger(const std::string& str)
{
	// 检查空字符串
	if (str.empty()) {
		return false;
	}

	// 检查首位字符：必须是1-9的数字（排除0和前导零）
	if (str[0] < '1' || str[0] > '9') {
		return false;
	}

	// 检查后续字符：必须全是0-9的数字
	for (size_t i = 1; i < str.length(); ++i) {
		if (!std::isdigit(static_cast<unsigned char>(str[i]))) {
			return false;
		}
	}

	return true;
}

bool GCAddon::SafeStringToInt(const std::string& str, uint32& result)
{
	if (!IsPositiveInteger(str)) {
		return false;
	}

	try {
		size_t pos = 0;
		uint32 value = std::stoul(str, &pos); // 使用无符号版本防止负数问题

		// 检查是否完整转换
		if (pos != str.size()) {
			return false; // 理论上不会发生，因IsPositiveInteger已验证
		}

		// 检查int范围溢出
		if (value > (std::numeric_limits<uint32>::max())) {
			return false;
		}

		result = value;
		return true;
	}
	catch (const std::invalid_argument&) {
		// 理论上不会触发，因IsPositiveInteger已过滤
		return false;
	}
	catch (const std::out_of_range&) {
		return false; // 处理超大数字（超过unsigned long范围）
	}
}
