﻿#pragma execution_character_set("utf-8")
#include "CreatureMod.h"
#include "../../Reward/Reward.h"
#include "../../CommonFunc/CommonFunc.h"
#include "../../String/String.h"
#include "../../Talisman/Talisman.h"
#include "../../Rank/Rank.h"
#include "../../AuthCheck/AuthCheck.h"
#include "Group.h"
#include <algorithm>
#include <random>
#include <regex>
#include "../../Instance/InstanceRankings.h"

std::vector<CreautreModTemplate> CreautreModVec;

std::vector<InstanceCreatureTemplate> InstanceCreatureVec;

void CreatureMod::Load()
{
	CreautreModVec.clear();
	uint32 count = 0;
	QueryResult result = WorldDatabase.PQuery(AuthFunc(AF_DAMAGE_REWARD)?
		//		0		1		2		3			4			5			6			7
		"SELECT 生物ID,等级,生命值,物理伤害值,法术伤害倍率,治疗效果倍率,减伤百分比,抗性值,"
		//8				9			10				11		12			13				14			15		16		17		18		19	
		"击杀奖励模板ID,击杀奖励几率,击杀是否全服提示,护甲值,攻击间隔,离开原位置重置距离,击杀奖励法宝值,掉落ID1,掉落ID2,掉落ID3,掉落ID4,掉落ID5,"
		//	20					21				22				23				24			25				26			27		28		29				30				31
		"队伍击杀奖励模板ID,队伍击杀奖励几率,击杀奖励自定义等级值,击杀召唤物体ID,副本挑战等级,是否加载原掉落,随机技能组模板ID,难度,生命值倍率,物理伤害倍率,击杀时最低伤害值_x,击杀时最低伤害奖励ID_x, 随机装备组索引ID_x, 附加随机掉落最小数量_x, 附加随机掉落最大数量_x, 随机装备掉落几率_x FROM _属性调整_生物" :
		//		0		1		2		3			4			5			6			7
		"SELECT 生物ID,等级,生命值,物理伤害值,法术伤害倍率,治疗效果倍率,减伤百分比,抗性值,"
		//8				9			10				11		12			13				14			15		16		17		18		19	
		"击杀奖励模板ID,击杀奖励几率,击杀是否全服提示,护甲值,攻击间隔,离开原位置重置距离,击杀奖励法宝值,掉落ID1,掉落ID2,掉落ID3,掉落ID4,掉落ID5,"
		//	20					21				22				23				24			25				26			27		28		29
		"队伍击杀奖励模板ID,队伍击杀奖励几率,击杀奖励自定义等级值,击杀召唤物体ID,副本挑战等级,是否加载原掉落,随机技能组模板ID,难度,生命值倍率,物理伤害倍率, 随机装备组索引ID_x, 附加随机掉落最小数量_x, 附加随机掉落最大数量_x, 随机装备掉落几率_x FROM _属性调整_生物"
	);

	if (result)
	{
		do
		{
			Field* fields = result->Fetch();
			CreautreModTemplate Temp;
			Temp.Entry = fields[0].GetUInt32();
			Temp.Level = fields[1].GetUInt8();
			Temp.Health = fields[2].GetUInt32();
			Temp.MeleeDmg = fields[3].GetUInt32();
			Temp.SpellDmgMod = fields[4].GetFloat();
			Temp.HealMod = fields[5].GetFloat();
			Temp.ReduceDmgPct = fields[6].GetFloat();
			Temp.Resistance = fields[7].GetInt32();
			Temp.KillRewId = fields[8].GetUInt32();
			Temp.KillRewChance = fields[9].GetFloat();
			Temp.KillAnnounce = fields[10].GetBool();
			Temp.Armor = fields[11].GetInt32();
			Temp.AttackTime = fields[12].GetUInt32();
			Temp.ResetDistance = fields[13].GetFloat();
			Temp.AddTalismanValue = fields[14].GetInt32();

			for (size_t i = 0; i < MAX_CUSTOM_LOOT_COUNT; i++)
				Temp.LootId[i] = fields[15 + i].GetUInt32();

			Temp.KillGroupRewId = fields[20].GetUInt32();
			Temp.KillGroupRewChance = fields[21].GetFloat();
			Temp.AddRankValue = fields[22].GetInt32();
			Temp.KillRewGameObject = fields[23].GetInt32();
			Temp.ChallengeLv = fields[24].GetUInt32();
			Temp.SrcLoot = fields[25].GetBool();
			Temp.RandSpellGroupId = fields[26].GetUInt32();
			Temp.Diff = fields[27].GetUInt32();
			Temp.HPMod = fields[28].GetFloat();
			Temp.MeleeDmgMod = fields[29].GetFloat();

			if (AuthFunc(AF_DAMAGE_REWARD))
			{
				Temp.DamageTakenAmount = fields[30].GetUInt32();
				Temp.DamageTakenRewId = fields[31].GetUInt32();

				Temp.ItemIndex = fields[32].GetInt32();
				Temp.MinLootCount = fields[33].GetUInt32();
				Temp.MaxLootCount = fields[34].GetUInt32();
				Temp.ItemIndexChance = fields[35].GetFloat();
			}
			else
			{
				Temp.DamageTakenAmount = 0;
				Temp.DamageTakenRewId = 0;

				Temp.ItemIndex = fields[30].GetInt32();
				Temp.MinLootCount = fields[31].GetUInt32();
				Temp.MaxLootCount = fields[32].GetUInt32();
				Temp.ItemIndexChance = fields[33].GetFloat();
			}
			
			CreautreModVec.push_back(Temp);
			count++;
		} while (result->NextRow());
	}
	LoadDBLog("_属性调整_生物", count);



	_CreaturesLootDataVec.clear();

	result = WorldDatabase.PQuery("SELECT 生物ID, 职业ID, 掉落组ID, 最小抽取个数, 最大抽取个数 from _额外掉落_生物");
	if (result)
	{
		uint32 count = 0;
		do
		{
			Field* fields = result->Fetch();
			CreaturesLootData Temp;
			Temp.entry = fields[0].GetUInt32();
			Temp.classid = fields[1].GetUInt32();
			Temp.groupid = fields[2].GetUInt32();
			Temp.count_min = fields[3].GetUInt32();
			Temp.count_max = fields[4].GetUInt32();
			_CreaturesLootDataVec.push_back(Temp);
			++count;
		} while (result->NextRow());
		LoadDBLog("_额外掉落_生物", count);
	}


	CreatureDeathTempMap.clear();
	CreatureDeathTempVec.clear();
	//										0				1				2				3				4						5							6					7		8			9
	result = WorldDatabase.PQuery("SELECT 生物ID, 击杀时血量百分比, 击杀时血量匹配方式, 触发几率, 击杀生物时使用的法术IDs, 击杀生物时使用的法术效果, 击杀生物时使用的光环效果, 召唤类型, 召唤物ID, 奖励模板ID from _生物_被击杀时");
	
	if (result)
	{
		uint32 count = 0;
		do
		{
			Field* fields = result->Fetch();

			CreatureDeathTemp Temp;

			Temp.entry = fields[0].GetUInt32();
			Temp.killedHealthPct = fields[1].GetUInt32();

			const char* str = fields[2].GetCString();
			if (strcmp("等于", str) == 0)
				Temp.ComMod = 比较_等于;
			else if (strcmp("大于", str) == 0)
				Temp.ComMod = 比较_大于;
			else if (strcmp("小于", str) == 0)
				Temp.ComMod = 比较_小于;
			else if (strcmp("大于等于", str) == 0)
				Temp.ComMod = 比较_大于等于;
			else if (strcmp("小于等于", str) == 0)
				Temp.ComMod = 比较_小于等于;

			Temp.chance = fields[3].GetFloat();

			Tokenizer data(fields[4].GetString(), ',');
			for (Tokenizer::const_iterator itr = data.begin(); itr != data.end(); ++itr)
				Temp.CurrentSpellIds.push_back(atoi(*itr));

			Temp.CurrentEffect = fields[5].GetUInt32();
			Temp.CurrentAura = fields[6].GetUInt32();

			const char* strs = fields[7].GetCString();
			if (strcmp("生物", strs) == 0)
				Temp.IsSumCreature = true;
			else if (strcmp("GameObject", strs) == 0)
				Temp.IsSumCreature = false;

			Temp.SummonId = fields[8].GetUInt32();
			Temp.RewId = fields[9].GetUInt32();

			CreatureDeathTempMap[Temp.entry] = Temp;
			CreatureDeathTempVec.push_back(Temp);
			++count;
		} while (result->NextRow());
		LoadDBLog("_生物_被击杀时", count);
	}
}

void CreatureMod::SetMod(Creature* creature)
{
	if (creature->GetEntry() == 1964 || creature->IsGuardian() || creature->IsHunterPet() || creature->IsTotem())
		return;

	////小动物之类
	//if (creature->IsCivilian())
	//	return;

	uint32 Entry = creature->GetEntry();
	uint32 ChallengeLv = creature->GetMap()->challengeLv;
	uint32 Diff = creature->GetMap()->GetDifficulty();

	for (auto itr = CreautreModVec.begin(); itr != CreautreModVec.end(); itr++)
	{
		if (Entry == itr->Entry && ChallengeLv == itr->ChallengeLv && Diff == itr->Diff)
		{
			creature->C_Level = itr->Level;
			creature->C_Health = itr->Health;
			creature->C_HpMod = itr->HPMod;
			creature->C_MeleeDmg = itr->MeleeDmg;
			creature->C_MeleeDmgMod = itr->MeleeDmgMod;
			creature->C_SpellDmgMod = itr->SpellDmgMod;
			creature->C_HealMod = itr->HealMod;
			creature->C_ReduceDmgPct = itr->ReduceDmgPct;
			creature->C_Resistance = itr->Resistance;
			creature->C_SrcLoot	= itr->SrcLoot;

			for (size_t i = 0; i < MAX_CUSTOM_LOOT_COUNT; i++)
				creature->C_LootId[i] = itr->LootId[i];

			creature->C_KillRewId = itr->KillRewId;
			creature->C_KillRewChance = itr->KillRewChance;
			creature->C_KillGroupRewId = itr->KillGroupRewId;
			creature->C_KillGroupRewChance = itr->KillGroupRewChance;
			creature->C_KillAnnounce = itr->KillAnnounce;
			creature->C_Armor = itr->Armor;
			creature->C_AttackTime = itr->AttackTime;
			creature->C_ResetDistance = itr->ResetDistance;
			creature->C_AddTalismanValue = itr->AddTalismanValue;
			creature->C_AddAddRankValue = itr->AddRankValue;
			creature->C_KillRewGameObject = itr->KillRewGameObject;
			creature->RandSpellGroupId = itr->RandSpellGroupId;

			creature->C_DamageTakenAmount = itr->DamageTakenAmount;
			creature->C_DamageTakenRewId = itr->DamageTakenRewId;

			creature->C_ItemIndex = itr->ItemIndex;
			creature->C_MinLootCount = itr->MinLootCount;
			creature->C_MaxLootCount = itr->MaxLootCount;
			creature->C_ItemIndexChance = itr->ItemIndexChance;
			break;
		}
	}
}

bool CreatureMod::HasSpells(uint32 entry)
{
	const CreatureTemplate * temp = sObjectMgr->GetCreatureTemplate(entry);

	if (temp)
	{
		for (uint8 i = 0; i < CREATURE_MAX_SPELLS; ++i)
		{
			uint32 spellid = temp->spells[i];

			if (spellid > 0)
				if (SpellEntry const* spellInfo = sSpellStore.LookupEntry(spellid))
					return true;
		}
	}

	return false;
}

void CreatureMod::GetCreatureExtraLootData(uint32 entry, uint32 classid, uint32 & groupid, uint32 &count)
{
	for (std::vector<CreaturesLootData>::iterator itr = _CreaturesLootDataVec.begin(); itr != _CreaturesLootDataVec.end(); ++itr)
	{
		if (entry == itr->entry && classid == itr->classid)
		{
			groupid = itr->groupid;
			std::random_device rd;
			std::mt19937 gen(rd());
			std::uniform_int_distribution<int> int_dist(itr->count_min, itr->count_max);
			count = int_dist(gen);
			break;
		}
	}
}

void CreatureMod::HandleCreatureKilledMod(Unit * killer, Unit * killed, uint32 CurrentHealth, SpellInfo const * CurrSpellInfo)
{
	if (!killer || !killed || killer->GetTypeId() != TYPEID_PLAYER || killed->GetTypeId() != TYPEID_UNIT || CurrentHealth == 0 || !CurrSpellInfo)
		return;

	uint32 entry = killed->GetEntry();

	std::map<uint32, CreatureDeathTemp>::iterator itr = CreatureDeathTempMap.find(entry);

	if (itr == CreatureDeathTempMap.end())
		return;

	if (!roll_chance_f(itr->second.chance))
		return;

	uint32 healthFromPct = killed->CountPctFromMaxHealth(itr->second.killedHealthPct);

	bool IsCom = true;
	switch (itr->second.ComMod)
	{
	case 比较_等于:
		if (!(CurrentHealth == healthFromPct))
			IsCom = false;
		break;
	case 比较_大于:
		if (!(CurrentHealth > healthFromPct))
			IsCom = false;
		break;
	case 比较_小于:
		if (!(CurrentHealth < healthFromPct))
			IsCom = false;
		break;
	case 比较_大于等于:
		if (!(CurrentHealth >= healthFromPct))
			IsCom = false;
		break;
	case 比较_小于等于:
		if (!(CurrentHealth <= healthFromPct))
			IsCom = false;
		break;
	default:
		return;
	}
	if (!IsCom)
		return;

	uint32 SpellId = CurrSpellInfo->Id;
	bool IsSpellId = itr->second.CurrentSpellIds.empty() ? true : false;
	for (auto its : itr->second.CurrentSpellIds)
	{
		if (its == SpellId)
			IsSpellId = true;
	}

	if (!IsSpellId)
		return;

	if (itr->second.CurrentEffect > 0)
	{
		if (!CurrSpellInfo->HasEffect(SpellEffects(itr->second.CurrentEffect)))
			return;
	}

	if (itr->second.CurrentAura > 0)
	{
		if (!CurrSpellInfo->HasAura(AuraType(itr->second.CurrentAura)))
			return;
	}


	if (itr->second.IsSumCreature)
		killer->SummonCreature(itr->second.SummonId, *killed, TEMPSUMMON_CORPSE_DESPAWN);
	else
		killer->SummonGameObject(itr->second.SummonId, killed->GetPositionX(), killed->GetPositionY(), killed->GetPositionZ(), 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f);

	sRew->Rew(killer->ToPlayer(), itr->second.RewId);

}

void CreatureMod::HandleCreatureKilledModVec(Unit * killer, Unit * killed, uint32 CurrentHealth, SpellInfo const * CurrSpellInfo)
{
	if (!killer || !killed || killer->GetTypeId() != TYPEID_PLAYER || killed->GetTypeId() != TYPEID_UNIT || CurrentHealth == 0 || !CurrSpellInfo)
		return;

	uint32 entry = killed->GetEntry();

	for (std::vector<CreatureDeathTemp>::iterator itr = CreatureDeathTempVec.begin(); itr != CreatureDeathTempVec.end(); ++itr)
	{
		if (itr->entry == entry)
		{
			if (!roll_chance_f(itr->chance))
				continue;;

			uint32 healthFromPct = killed->CountPctFromMaxHealth(itr->killedHealthPct);

			bool IsCom = true;
			switch (itr->ComMod)
			{
			case 比较_等于:
				if (!(CurrentHealth == healthFromPct))
					IsCom = false;
				break;
			case 比较_大于:
				if (!(CurrentHealth > healthFromPct))
					IsCom = false;
				break;
			case 比较_小于:
				if (!(CurrentHealth < healthFromPct))
					IsCom = false;
				break;
			case 比较_大于等于:
				if (!(CurrentHealth >= healthFromPct))
					IsCom = false;
				break;
			case 比较_小于等于:
				if (!(CurrentHealth <= healthFromPct))
					IsCom = false;
				break;
			default:
				return;
			}
			if (!IsCom)
				continue;

			uint32 SpellId = CurrSpellInfo->Id;
			bool IsSpellId = itr->CurrentSpellIds.empty() ? true : false;
			for (auto its : itr->CurrentSpellIds)
			{
				if (its == SpellId)
					IsSpellId = true;
			}

			if (!IsSpellId)
				continue;

			if (itr->CurrentEffect > 0)
			{
				if (!CurrSpellInfo->HasEffect(SpellEffects(itr->CurrentEffect)))
					continue;
			}

			if (itr->CurrentAura > 0)
			{
				if (!CurrSpellInfo->HasAura(AuraType(itr->CurrentAura)))
					continue;
			}

			if (itr->SummonId > 0)
			{
				if (itr->IsSumCreature)
					killer->SummonCreature(itr->SummonId, *killed, TEMPSUMMON_CORPSE_DESPAWN);
				else
					killer->SummonGameObject(itr->SummonId, killed->GetPositionX(), killed->GetPositionY(), killed->GetPositionZ(), 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f);
			}

			sRew->Rew(killer->ToPlayer(), itr->RewId);
			break;	//代码缺漏，优化代码
		}
	}
}

class CreatureKillRew : PlayerScript
{
public:
	CreatureKillRew() : PlayerScript("CreatureKillRew") {}

	void OnCreatureKill(Player* killer, Creature* killed) override
	{
		if (killed->C_KillRewGameObject != 0)
			killer->SummonGameObject(killed->C_KillRewGameObject, killed->GetPositionX(), killed->GetPositionY(), killed->GetPositionZ(), killed->GetOrientation(), 0, 0, 0, 0, 0);

		//击杀奖励
		if (killed->C_KillRewId != 0 && frand(0, 100) <= killed->C_KillRewChance)
			sRew->Rew(killer, killed->C_KillRewId);

		//队伍击杀奖励
		if (killed->C_KillGroupRewId != 0)
			if (Group* group = killer->GetGroup())
				for (GroupReference* itr = group->GetFirstMember(); itr != NULL; itr = itr->next())
					if (Player* member = itr->GetSource())
						if (member->IsInWorld() && member->GetGUID() != killer->GetGUID() && frand(0, 100) <= killed->C_KillGroupRewChance && member->IsSelfOrInSameMap(killer) && member->GetDistance(killer) <= 200.0f)
							sRew->Rew(member, killed->C_KillGroupRewId);

		//击杀广播
		if (killed->C_KillAnnounce)
		{
			//const char*msg;

			//if (killer->GetGroup())
			//	msg = sString->Format(sString->GetText(STR_GROUP_KILL_CREATURE), sCF->GetNameLink(killer).c_str(), sCF->GetNameLink(killed).c_str());
			//else
			//	msg = sString->Format(sString->GetText(STR_KILL_CREATRE), sCF->GetNameLink(killer).c_str(), sCF->GetNameLink(killed).c_str());

			//std::ostringstream oss;
			std::string NowMsg = "";

			uint32 t = killed->JustDiedTime - killed->EnterCombatTime;

			if (t >= 0 && t < 1200)
			//	oss << msg << "击杀用时" << SecTimeString(t, true);
			//else
			//	oss << msg;

			NowMsg = std::string("击杀用时") + SecTimeString(t, true);
			
			//sWorld->SendServerMessage(SERVER_MSG_STRING, oss.str().c_str());

			if (killer->GetGroup())
			{
				sString->SendSerMessage(sString->GetText(STR_GROUP_KILL_CREATURE), sCF->GetNameLink(killer).c_str(), sCF->GetNameLink(killed).c_str(), NowMsg.c_str());
			}
			else
			{
				sString->SendSerMessage(sString->GetText(STR_KILL_CREATRE), sCF->GetNameLink(killer).c_str(), sCF->GetNameLink(killed).c_str(), NowMsg.c_str());
			}

			//sWorld->SendServerMessage(SERVER_MSG_STRING, oss.str().c_str());
		}

		//副本BOSS竞速排行榜
		uint32 timediff = killed->JustDiedTime - killed->EnterCombatTime;
		if (timediff > 5)
		{
			Map * map = killer->GetMap();
			uint32 mapid = map->GetId();
			if (sInstanceRanks->IsKillBossRankMap(mapid))
			{
				uint32 ChallengeLv = map->challengeLv;
				uint32 diff = map->GetDifficulty();
				uint32 entry = killed->GetEntry();
				if (sInstanceRanks->IsInBossIds(mapid, ChallengeLv, diff, entry))
					sInstanceRanks->ReplaceIntoKillBossLogsData(mapid, killer, entry, timediff);
			}
		}

		//Rank值 正数-击杀者 负数-整个队伍
		if (killed->C_AddAddRankValue > 0)
			sRank->Update(killer, killed->C_AddAddRankValue, true);
		else if (killed->C_AddAddRankValue < 0)
		{
			if (Group* group = killer->GetGroup())
			{
				for (GroupReference* itr = group->GetFirstMember(); itr != NULL; itr = itr->next())
					if (Player* member = itr->GetSource())
						if (member->IsInWorld() && member->GetDistance(killer) <= 200.0f)
							sRank->Update(member, abs(killed->C_AddAddRankValue), true);
			}
			else
				sRank->Update(killer, abs(killed->C_AddAddRankValue), true);
		}

		//法宝值 正数-击杀者 负数-整个队伍
		if (killed->C_AddTalismanValue > 0)
		{
			sTalisman->AddTalismanValue(killer, killed->C_AddTalismanValue, true);
			if (killed->IsDungeonBoss() || killed->IsDungeonBoss())
				sTalisman->SaveTalisManValue(killer);
		}		
		else if (killed->C_AddTalismanValue < 0)
		{
			if (Group* group = killer->GetGroup())
			{
				for (GroupReference* itr = group->GetFirstMember(); itr != NULL; itr = itr->next())
					if (Player* member = itr->GetSource())
						if (member->IsInWorld() && member->GetDistance(killer) <= 200.0f)
						{
							sTalisman->AddTalismanValue(member, abs(killed->C_AddTalismanValue), true);

							if (killed->IsDungeonBoss() || killed->IsDungeonBoss())
								sTalisman->SaveTalisManValue(member);
						}
			}
			else
			{
				sTalisman->AddTalismanValue(killer, abs(killed->C_AddTalismanValue), true);

				if (killed->IsDungeonBoss() || killed->IsDungeonBoss())
					sTalisman->SaveTalisManValue(killer);
			}
		}
	}
};

class GoblinScript : public CreatureScript
{
public:
	GoblinScript() : CreatureScript("GoblinScript") { }

	struct GoblinScriptAI : public ScriptedAI
	{
		GoblinScriptAI(Creature* creature) : ScriptedAI(creature), Summons(me)
		{
			_timer = 0;
			_summoned = false;
			me->GetMotionMaster()->MoveRandom(5.0f);
		}

		SummonList Summons;

		void Reset() override
		{
			
		}

		void UpdateAI(uint32 diff)
		{
			if (_summoned)
				return;

			_timer += diff;

			if (_timer > 5 * IN_MILLISECONDS)
			{
				_summoned = true;
				me->CastSpell(me, 666665, false);

				if (roll_chance_f(30))
				{
					me->MonsterYell("菜逼们！先别回城！老子正在建立通往彩虹秘境的通道！", LANG_UNIVERSAL, NULL);
				}
				else
				{
					me->MonsterYell("谢谢你,勇者！", LANG_UNIVERSAL, NULL);
					me->MonsterYell("为了感谢你，我将打开通往彩虹秘境的传送门！", LANG_UNIVERSAL, NULL);
				}	
			}
		}
	private:
		uint32 _timer;
		bool _summoned;
	};

	CreatureAI* GetAI(Creature* creature) const
	{
		return new GoblinScriptAI(creature);
	}
};

void AddSC_CreatureKillRew()
{
	new CreatureKillRew();
	new GoblinScript();
}