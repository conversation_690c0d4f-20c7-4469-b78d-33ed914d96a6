#include "InstanceRankings.h"
#include "..\CommonFunc\CommonFunc.h"
#include "..\GCAddon\GCAddon.h"
#include "World.h"
#include "..\Switch\Switch.h"
#include <ctime>

#pragma execution_character_set("utf-8")

std::map<uint32, InstanceKillBossRanksTemplate> InstanceKillBossRanksMap;
std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>> PlayerKillBossTotalTimesMap;
std::unordered_map<uint32/*guidlow*/, std::string> PlayerNameMap;
std::vector<SendRewLog> SendRewPlayerLogVec;
std::vector<rewDateLog> rewDateLogVec;

void InstanceRankings::Load(bool online)
{
	rewDateLogVec.clear();

	QueryResult result = CharacterDatabase.PQuery("SELECT 日期, 是否发放奖励 FROM `_副本_击杀boss发奖日期记录`");

	if (result)
	{
		uint32 count = 0;
		do
		{
			Field* fields = result->Fetch();
			rewDateLog Temp;

			std::string m_data = fields[0].GetString();
			std::vector<std::string> vec = sCF->SplitStr(m_data, "-");
			Temp.rewYear = atoi(vec[0].c_str());
			Temp.rewMon = atoi(vec[1].c_str());
			Temp.rewDay = atoi(vec[2].c_str());

			Temp.IsLogs = fields[1].GetUInt32() > 0 ? true : false;

			rewDateLogVec.push_back(Temp);
			//sLog->outString("rewDateLogVec : %u %u %u : %u", Temp.rewYear, Temp.rewMon, Temp.rewDay, Temp.IsLogs ? 1:0);
			++count;
		} while (result->NextRow());
		LoadDBLog("_副本_击杀boss发奖日期记录", count);
	}


	InstanceKillBossRanksMap.clear();

	//													0		1		2		  3			 4
	result = WorldDatabase.PQuery("SELECT 地图ID, 难度, 挑战难度, BOSS列表, 名次奖励列表 FROM _副本_击杀BOSS竞速排行榜");

	if (result)
	{
		uint32 count = 0;

		do
		{
			Field* fields = result->Fetch();
			
			InstanceKillBossRanksTemplate temp;

			temp.MapId = fields[0].GetUInt32();
			temp.Diff = fields[1].GetUInt32();
			temp.ChallengeLv = fields[2].GetUInt32();

			Tokenizer BossIds(fields[3].GetString(), ',');
			for (Tokenizer::const_iterator itr = BossIds.begin(); itr != BossIds.end(); ++itr)
			{
				uint32 bossid = atoi(*itr);
				CreatureTemplate const * info = sObjectMgr->GetCreatureTemplate(bossid);
				if (!info)
				{
					sLog->outError("_副本_击杀BOSS竞速排行榜 地图ID = %u | Boss列表中：%u 不是正确生物ID", temp.MapId, bossid);
					ASSERT(false);
				}

				temp.BossIds.push_back(bossid);
			}

			Tokenizer RewDatas(fields[4].GetString(), '#');
			for (Tokenizer::const_iterator itr = RewDatas.begin(); itr != RewDatas.end(); ++itr)
			{
				std::string rewInfo = *itr;
				std::vector<std::string> str_vec = sCF->SplitStr(rewInfo, ",");
				if (str_vec.size() != 3)
					ASSERT(false);

				uint32 rank = atoi(str_vec[0].c_str());
				uint32 itemid = atoi(str_vec[1].c_str());
				uint32 itemcount = atoi(str_vec[2].c_str());
				//sLog->outString("_副本_击杀BOSS竞速排行榜 地图 = %u | 名次 = %u | 奖励物品ID = %u | 数量ID %u", temp.MapId, rank, itemid, itemcount);
				temp.RankRewData[rank] = { itemid, itemcount };
			}

			InstanceKillBossRanksMap[temp.MapId] = temp;

			count++;
		} while (result->NextRow());

		LoadDBLog("_副本_击杀BOSS竞速排行榜", count);
	}

	PlayerKillBossTotalTimesMap.clear();
	//											 0		  1			2		  3				4
	result = CharacterDatabase.PQuery("SELECT 地图ID, 玩家GUID, 总用时, 最快详细用时, 当前详细用时 FROM `_副本_击杀boss计时记录表` ORDER BY `地图ID` ASC, `总用时` ASC");
	//首次成绩肯定只载入最快成绩 之后每次成绩都先载入当前成绩 然后当前成绩通关后 统计时间小于最快成绩则替换不小于则等待下一次清零 下一次清零的时机在下一次打同一只boss无论那只都全部清零
	if (result)
	{
		uint32 count = 0;

		uint32 CurrentMap = 0;
		uint32 rank = 1;
		do
		{
			Field* fields = result->Fetch();

			uint32 mapid = fields[0].GetUInt32();
			uint32 playerGuid = fields[1].GetUInt32();
			uint32 TotalTime = fields[2].GetUInt32();

			std::map<uint32/*bossid*/, uint32/*time*/> bestKillBossTimer;
			Tokenizer bestBossIds(fields[3].GetString(), '#');
			for (Tokenizer::const_iterator itr = bestBossIds.begin(); itr != bestBossIds.end(); ++itr)
			{
				std::string rewInfo = *itr;
				std::vector<std::string> str_vec = sCF->SplitStr(rewInfo, ",");
				if (str_vec.size() != 2)
					ASSERT(false);

				uint32 bossid = atoi(str_vec[0].c_str());
				uint32 times = atoi(str_vec[1].c_str());
				bestKillBossTimer[bossid] = times;
			}

			std::map<uint32/*bossid*/, uint32/*time*/> currKillBossTimer;
			Tokenizer currBossIds(fields[4].GetString(), '#');
			for (Tokenizer::const_iterator itr = currBossIds.begin(); itr != currBossIds.end(); ++itr)
			{
				std::string rewInfo = *itr;
				std::vector<std::string> str_vec = sCF->SplitStr(rewInfo, ",");
				if (str_vec.size() != 2)
					ASSERT(false);

				uint32 bossid = atoi(str_vec[0].c_str());
				uint32 times = atoi(str_vec[1].c_str());
				currKillBossTimer[bossid] = times;
			}

			if (CurrentMap != mapid)
			{
				CurrentMap = mapid;
				rank = 1;
			}

			PlayerKillBossTotalTimesMap[mapid].push_back({ rank, playerGuid, TotalTime, bestKillBossTimer, currKillBossTimer });

			++rank;
			count++;
		} while (result->NextRow());

		LoadDBLog("_副本_击杀BOSS计时记录表", count);
	}

	SendRewPlayerLogVec.clear();

	//												0	   1	  2			3			4			5
	result = CharacterDatabase.PQuery("SELECT 玩家GUID ,地图ID, 日期, 奖励物品列表, 是否发放奖励, 名次 FROM `_副本_击杀boss发奖记录表`");

	if (result)
	{
		uint32 count = 0;
		do 
		{
			Field* fields = result->Fetch();
			SendRewLog Temp;

			Temp.playerguid = fields[0].GetUInt32();
			Temp.mapid = fields[1].GetUInt32();

			std::string m_data = fields[2].GetString();
			std::vector<std::string> vec = sCF->SplitStr(m_data, "-");
			Temp.rewYear = atoi(vec[0].c_str());
			Temp.rewMon = atoi(vec[1].c_str());
			Temp.rewDay = atoi(vec[2].c_str());

			std::string m_rew = fields[3].GetString();
			std::vector<std::string> rew = sCF->SplitStr(m_rew, ",");
			Temp.itemid = atoi(rew[0].c_str());
			Temp.itemcount = atoi(rew[1].c_str());

			Temp.IsSendRew = fields[4].GetUInt32() > 0 ? true : false;
			Temp.rank = fields[5].GetUInt32();

			//sLog->outString("玩家GUID：【%u】，地图ID：【%u】，地图名称：【%s】，名次：【%u】, 日期【%u %u %u】,奖励物品ID【%u】，数量【%u】，是否发放：【%s】",Temp.playerguid, Temp.mapid, GetMapNameById(Temp.mapid).c_str(), Temp.rank, Temp.rewYear, Temp.rewMon, Temp.rewDay, Temp.itemid, Temp.itemcount, (Temp.IsSendRew ? "是" : "否"));
			
			SendRewPlayerLogVec.push_back(Temp);

			++count;
		} while (result->NextRow());
		LoadDBLog("_副本_击杀boss发奖记录表", count);
	}

	if (!online)
	{
		PlayerNameMap.clear();

		result = CharacterDatabase.PQuery("SELECT count(guid) FROM characters");

		if (result)
		{
			Field* f = result->Fetch();
			uint32 maxCount = f[0].GetUInt32();

			PlayerNameMap.reserve(maxCount); 
		}

		result = CharacterDatabase.PQuery("SELECT guid, name FROM characters");
		if (result)
		{
			do
			{
				Field* fields = result->Fetch();
				uint32 guidlow = fields[0].GetUInt32();
				std::string name = fields[1].GetString();

				PlayerNameMap[guidlow] = name;

			} while (result->NextRow());
		}
	}
}

void InstanceRankings::IntoPlayerNameMap(Player * player)
{
	uint32 guid = player->GetGUIDLow();

	auto itr = PlayerNameMap.find(guid);

	if (itr == PlayerNameMap.end())
		return;

	PlayerNameMap[guid] = player->GetName();
}

std::string InstanceRankings::GetPlayerName(uint32 guidlow)
{
	auto itr = PlayerNameMap.find(guidlow);

	return itr == PlayerNameMap.end() ? "" : itr->second;
}

void InstanceRankings::GetBossIds(uint32 map, std::vector<uint32> & bossids)
{
	std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.find(map);

	if (itr == InstanceKillBossRanksMap.end())
		return;

	bossids = itr->second.BossIds;
}

bool InstanceRankings::IsInBossIds(uint32 mapid, uint32 ChallengeLv, uint32 diff, uint32 entry)
{
	std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.find(mapid);

	if (itr == InstanceKillBossRanksMap.end())
		return false;

	if (itr->second.ChallengeLv == ChallengeLv && itr->second.Diff == diff)
		for (std::vector<uint32>::iterator iter = itr->second.BossIds.begin(); iter != itr->second.BossIds.end(); ++iter)
			if (*iter == entry)
			{
				//sLog->outString("副本地图ID：【%u】 | 生物ID：【%u】| 存在于数据库配置",mapid, entry);
				return true;
			}
				
	//sLog->outString("副本地图ID：【%u】 | 生物ID：【%u】| 数据库配置无此数据", mapid, entry);
	return false;
}

bool InstanceRankings::IsKillBossRankMap(uint32 mapid)
{
	std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.find(mapid);

	return itr != InstanceKillBossRanksMap.end();	//true 存在 
}

void InstanceRankings::GetKillBossTimeRankRew(uint32 mapid, uint32 rank, uint32 & itemid, uint32 & itemcount)
{
	std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.find(mapid);

	if (itr == InstanceKillBossRanksMap.end())
		return;

	itemid = sSwitch->GetValueByIndex(ST_副本竞速通用奖励, 1);
	itemcount = sSwitch->GetValueByIndex(ST_副本竞速通用奖励, 1);

	std::map<uint32, RankingRewTemp>::iterator ite = itr->second.RankRewData.find(rank);

	if (ite == itr->second.RankRewData.end())
		return;

	itemid = ite->second.itemid;
	itemcount = ite->second.count;
}

bool InstanceRankings::IsKillCurrAllBoss(uint32 mapid, uint32 guidlow, uint32 & currTotalTime)
{
	std::vector<uint32> bossidsVec;
	GetBossIds(mapid, bossidsVec);
	if (bossidsVec.empty())
		return false;

	std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.find(mapid);

	if (itr != PlayerKillBossTotalTimesMap.end())
	{
		for (std::vector<PlayerKillBossTotalTimes>::iterator DataItr = itr->second.begin(); DataItr != itr->second.end(); ++DataItr)
		{
			if (DataItr->playerGuid == guidlow)
			{
				bool iskillAll = true;

				for (auto &bossid : bossidsVec)
				{
					std::map<uint32/*bossid*/, uint32/*time*/>::iterator iter = DataItr->CurrKillBossTimes.find(bossid);
					if (iter == DataItr->CurrKillBossTimes.end())
					{
						currTotalTime = 0;
						iskillAll = false;
						break;
					}
					currTotalTime += iter->second;
				}

				return iskillAll;
			}
		}
	}

	return false;
}

bool InstanceRankings::IsKillBestAllBoss(uint32 mapid, uint32 guidlow, uint32 & bestTotalTime)
{
	std::vector<uint32> bossidsVec;
	GetBossIds(mapid, bossidsVec);
	if (bossidsVec.empty())
		return false;

	std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.find(mapid);

	if (itr != PlayerKillBossTotalTimesMap.end())
	{
		for (std::vector<PlayerKillBossTotalTimes>::iterator DataItr = itr->second.begin(); DataItr != itr->second.end(); ++DataItr)
		{
			if (DataItr->playerGuid == guidlow)
			{
				bool iskillAll = true;

				for (auto &bossid : bossidsVec)
				{
					std::map<uint32/*bossid*/, uint32/*time*/>::iterator iter = DataItr->bestKillBossTimes.find(bossid);
					if (iter == DataItr->bestKillBossTimes.end())
					{
						bestTotalTime = 0;
						iskillAll = false;
						break;
					}
					bestTotalTime += iter->second;
				}

				return iskillAll;
			}
		}
	}

	return false;
}

void InstanceRankings::ClearKillBossDataForPlayer(Player * player, uint32 mapid, bool isAll /*= false*/)
{
	uint32 guidlow = player->GetGUIDLow();

	if (isAll)
	{
		for (std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.begin(); itr != PlayerKillBossTotalTimesMap.end(); ++itr)
		{
			for (std::vector<PlayerKillBossTotalTimes>::iterator ite = itr->second.begin(); ite != itr->second.end(); ++ite)
			{
				if (ite->playerGuid == guidlow)
				{
					itr->second.erase(ite);
					break;
				}
			}
		}

		return;
	}

	std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.find(mapid);

	if (itr == PlayerKillBossTotalTimesMap.end())
		return;

	for (std::vector<PlayerKillBossTotalTimes>::iterator ite = itr->second.begin(); ite != itr->second.end(); ++ite)
	{
		if (ite->playerGuid == guidlow)
		{
			itr->second.erase(ite);
			break;
		}
	}
	
}

void InstanceRankings::ReplaceIntoKillBossLogsData(uint32 map, Player * player, uint32 entry, uint32 times)
{
	bool HasData = false;

	uint32 guid = player->GetGUIDLow();

	std::vector<uint32> bossidsVec;
	GetBossIds(map, bossidsVec);
	if (bossidsVec.empty())
		return;
	
	std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.find(map);

	if (itr != PlayerKillBossTotalTimesMap.end())
	{
		bool IsSort = false;

		for (std::vector<PlayerKillBossTotalTimes>::iterator DataItr = itr->second.begin(); DataItr != itr->second.end(); ++DataItr)
		{
			if (DataItr->playerGuid == guid)
			{
				HasData = true;

				uint32 bestTotalTime = 0;
				bool isHasBestTime = IsKillBestAllBoss(map, guid, bestTotalTime);
				uint32 currTotalTime = 0;
				bool isHasCurrTime = false;
				std::map<uint32/*bossid*/, uint32/*time*/>::iterator bestKillTimeItr = DataItr->bestKillBossTimes.find(entry);

				//没有首次通关成绩，则一直写入最佳详细成绩
				if (!isHasBestTime)
				{
					DataItr->bestKillBossTimes[entry] = times;
					//如果上面打的是最后一个BOSS，则再次获取可以进行正确判断
					isHasBestTime = IsKillBestAllBoss(map, guid, bestTotalTime);
				}
				else
				{
					isHasCurrTime = IsKillCurrAllBoss(map, guid, currTotalTime);
					//如果存在当前成绩，则清空全部成绩后重新记录成绩
					if (isHasCurrTime)
						DataItr->CurrKillBossTimes.clear();

					DataItr->CurrKillBossTimes[entry] = times;
					//如果上面打的是最后一个BOSS，则再次获取可以进行正确判断
					isHasCurrTime = IsKillCurrAllBoss(map, guid, currTotalTime);
				}

				//如果最佳成绩和当前成绩都通关
				if (isHasBestTime && isHasCurrTime)
				{
					if (bestTotalTime > currTotalTime)
					{
						IsSort = true;
						DataItr->TotalTime = currTotalTime;
						DataItr->bestKillBossTimes = DataItr->CurrKillBossTimes;
					}
				}

				//首次通关
				if (isHasBestTime && DataItr->CurrKillBossTimes.empty())
				{
					IsSort = true;
					DataItr->TotalTime = bestTotalTime;
				}

				//写表是一定的
				SaveLogsToDB(map, guid, DataItr->TotalTime, DataItr->bestKillBossTimes, DataItr->CurrKillBossTimes);
				break;
			}
		}

		if (IsSort)
		{
			std::sort(itr->second.begin(), itr->second.end(), [](const PlayerKillBossTotalTimes& a, const PlayerKillBossTotalTimes& b)
			{
				return a.TotalTime < b.TotalTime;
			});

			uint32 rankcount = 1;
			for (std::vector<PlayerKillBossTotalTimes>::iterator KillBossDataItr = itr->second.begin(); KillBossDataItr != itr->second.end(); ++KillBossDataItr)
			{
				if (KillBossDataItr->TotalTime == TimerInitValue)
					continue;

				KillBossDataItr->rank = rankcount;
				++rankcount;
				//sLog->outString("重新排序后的名次：第【%u】名 | 玩家GUID：【%u】| 玩家名字：【%s】 | 总用时：【%s】", KillBossDataItr->rank, KillBossDataItr->playerGuid, GetPlayerName(KillBossDataItr->playerGuid).c_str(), SecTimeString(KillBossDataItr->TotalTime,true).c_str());
			}

			HandleSendKillBossTimerDataToAllPlayer(map);
		}

		SendPlayerOwnerKillBossTimeData(player, map);
	}
	
	if (HasData)
		return;

	PlayerKillBossTotalTimes temp;
	temp.rank = 0;
	temp.TotalTime = TimerInitValue;
	temp.playerGuid = guid;
	temp.bestKillBossTimes[entry] = times;
	temp.CurrKillBossTimes;

	PlayerKillBossTotalTimesMap[map].push_back(temp);

	//写表
	SaveLogsToDB(map, guid, TimerInitValue, temp.bestKillBossTimes, temp.CurrKillBossTimes);
}

void InstanceRankings::SaveLogsToDB(uint32 mapid, uint32 guid, uint32 TotalTime, std::map<uint32/*bossid*/, uint32/*time*/> bestKillBossTime, std::map<uint32/*bossid*/, uint32/*time*/> currKillBossTime)
{
	
	std::ostringstream bestmsg;
	for (auto &itr : bestKillBossTime)
		bestmsg << itr.first << "," << itr.second << "#";
	
	std::string beststr = bestmsg.str();
	beststr.pop_back();

	std::ostringstream currmsg;
	for (auto &itr : currKillBossTime)
		currmsg << itr.first << "," << itr.second << "#";

	std::string currstr = currmsg.str();
	if (!currstr.empty())
		currstr.pop_back();

	PreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_KILL_BOSS_RANKING_LOGS);

	stmt->setUInt32(0, mapid);
	stmt->setUInt32(1, guid);
	stmt->setUInt32(2, TotalTime);
	stmt->setString(3, beststr);
	stmt->setString(4, currstr);

	CharacterDatabase.Execute(stmt);
}

void InstanceRankings::ClearLogsDB()
{
	CharacterDatabase.PExecute("TRUNCATE TABLE `_副本_击杀boss计时记录表`");
	PlayerKillBossTotalTimesMap.clear();

	for (auto itr = sWorld->GetAllSessions().begin(); itr != sWorld->GetAllSessions().end(); ++itr)
	{
		if (Player * player = itr->second->GetPlayer())
		{
			//发送指令给客户端要求客户端清空
			sGCAddon->SendPacketTo(player, "CLEARKILLBOSS", "");
		}
	}
}

void InstanceRankings::SendKillBossRankRewards()
{
	CharacterDatabase.PExecute("TRUNCATE TABLE `_副本_击杀boss发奖记录表`");
	CharacterDatabase.PExecute("TRUNCATE TABLE `_副本_击杀boss发奖日期记录`");
	SendRewPlayerLogVec.clear();
	rewDateLogVec.clear();

	time_t t = time(NULL);
	tm localTime;
	localtime_s(&localTime, &t);

	//// 获取当前时间
	//std::time_t now = std::time(nullptr);

	//// 将时间转换为本地时间
	//std::tm* localTime = std::localtime(&now);

	int year = localTime.tm_year + 1900;
	int month = localTime.tm_mon + 1;
	int day = localTime.tm_mday;
	int hour = localTime.tm_hour;
	int min = localTime.tm_min;
	int sec = localTime.tm_sec;
	int wday = localTime.tm_wday;

	//sLog->outString("开始发放 %u - %u - %u 排行榜奖励...",year, month, day);

	for (std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.begin(); itr != PlayerKillBossTotalTimesMap.end(); ++itr)
	{
		uint32 mapid = itr->first;

		for (std::vector<PlayerKillBossTotalTimes>::iterator ite = itr->second.begin(); ite != itr->second.end(); ++ite)
		{
			uint32 rank = ite->rank;
			if (rank == 0 || ite->TotalTime == TimerInitValue)
				continue;

			uint32 itemid = 0;
			uint32 itemcount = 0;
			GetKillBossTimeRankRew(mapid, rank, itemid, itemcount);

			if (itemid == 0 || itemcount == 0)
				continue;

			uint32 guidlow = ite->playerGuid;
			uint64 guid = MAKE_NEW_GUID(guidlow, 0, HIGHGUID_PLAYER);
			Player* player = ObjectAccessor::FindPlayer(guid);

			if (!player)
			{
				std::ostringstream rews;

				rews << itemid << "," << itemcount;

				//REPLACE INTO `_副本_击杀boss发奖记录表` (`玩家GUID`, `地图ID`, `名次`, `日期`, `奖励物品列表`, `是否发放奖励`) VALUE (?, ?, ?, NOW(), ?, ?)
				PreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_KILL_BOSS_RANK_SENDREW_LOGS);

				stmt->setUInt32(0, guidlow);
				stmt->setUInt32(1, mapid);
				stmt->setUInt32(2, rank);
				stmt->setString(3, rews.str());	//奖励物品列表
				stmt->setUInt32(4, 0);

				CharacterDatabase.Execute(stmt);
				//sLog->outString("未发放【%u - %u - %u】排行榜奖励：地图ID：【%u】，地图：【%s】，玩家GUID：【%u】，玩家名字：【%s】，奖励物品ID：【%u】，数量：【%u】", year, month, day, mapid, GetMapNameById(mapid).c_str(), ite->playerGuid, GetPlayerName(guidlow), itemid, itemcount);
				SendRewPlayerLogVec.push_back({ guidlow, mapid, rank, year, month, day, itemid, itemcount, false });
				continue;
			}
				

			if (itemcount > 0)
			{
				if (ItemTemplate const* pProto = sObjectMgr->GetItemTemplate(itemid))
				{
					ChatHandler(player->GetSession()).PSendSysMessage("你收到新邮件:%s X %u", sCF->GetItemLink(itemid).c_str(), itemcount);

					MailSender sender(MAIL_NORMAL, 0, MAIL_STATIONERY_GM);

					std::ostringstream strm;

					strm << "你在副本：【" << GetMapNameById(mapid) << "】竞速活动中获得第【" << rank << "】名，请查收你的奖励！";

					MailDraft draft("排行榜奖励", strm.str());

					SQLTransaction trans = CharacterDatabase.BeginTransaction();

					uint32 stack = pProto->Stackable > 0 ? pProto->Stackable : 1;

					while (itemcount > stack)
					{
						if (Item* item = Item::CreateItem(itemid, stack))
						{
							item->SaveToDB(trans);
							draft.AddItem(item);
						}
						itemcount -= stack;
					}

					if (Item* item = Item::CreateItem(itemid, itemcount))
					{
						item->SaveToDB(trans);
						draft.AddItem(item);
					}

					draft.SendMailTo(trans, MailReceiver(NULL, player->GetGUIDLow()), sender);
					CharacterDatabase.CommitTransaction(trans);

					//sLog->outString("发放【%u - %u - %u】排行榜奖励：地图ID：【%u】，地图：【%s】，玩家GUID：【%u】，玩家名字：【%s】，奖励物品ID：【%u】，数量：【%u】",year, month, day, mapid, GetMapNameById(mapid).c_str(), ite->playerGuid, player->GetName().c_str(), itemid, itemcount);

					std::ostringstream rews;

					rews << itemid << "," << itemcount;

					//REPLACE INTO `_副本_击杀boss发奖记录表` (`玩家GUID`, `地图ID`, `名次`, `日期`, `奖励物品列表`, `是否发放奖励`) VALUE (?, ?, ?, NOW(), ?, ?)
					PreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_KILL_BOSS_RANK_SENDREW_LOGS);

					stmt->setUInt32(0, ite->playerGuid);
					stmt->setUInt32(1, mapid);
					stmt->setUInt32(2, rank);
					stmt->setString(3, rews.str());	//奖励物品列表
					stmt->setUInt32(4, 1);

					CharacterDatabase.Execute(stmt);

					SendRewPlayerLogVec.push_back({ guidlow, mapid, rank, year, month, day, itemid, itemcount, true });
				}
			}
		}
	}

	//这一天的排名奖励已经被记录
	rewDateLogVec.push_back({ year, month, day, true });

	CharacterDatabase.PExecute("REPLACE INTO `_副本_击杀boss发奖日期记录` (`日期`, `是否发放奖励`) VALUE (NOW(), 1)");
}

void InstanceRankings::SendPlayerKillBossRankRewards(Player * player)
{
	uint32 guidlow = player->GetGUIDLow();

	//sLog->outString("SendPlayerKillBossRankRewards guid = %u", guidlow);

	for (std::vector<SendRewLog>::iterator itr = SendRewPlayerLogVec.begin(); itr != SendRewPlayerLogVec.end(); ++itr)
	{
		//sLog->outString("SendPlayerKillBossRankRewards itr->playerguid = %u | IsSendRew = %u | itemid = %u | count = %u", itr->playerguid, itr->IsSendRew ? 1 : 0, itr->itemid, itr->itemcount);

		if (!itr->IsSendRew && itr->playerguid == guidlow)
		{
			uint32 mapid = itr->mapid;
			uint32 rank = itr->rank;
			uint32 itemid = itr->itemid;
			uint32 itemcount = itr->itemcount;
			uint32 year = itr->rewYear;
			uint32 month = itr->rewMon;
			uint32 day = itr->rewDay;
			if (itemid == 0 || itemcount == 0)
				continue;

			if (itemcount > 0)
			{
				if (ItemTemplate const* pProto = sObjectMgr->GetItemTemplate(itemid))
				{
					ChatHandler(player->GetSession()).PSendSysMessage("你收到新邮件:%s X %u", sCF->GetItemLink(itemid).c_str(), itemcount);

					MailSender sender(MAIL_NORMAL, 0, MAIL_STATIONERY_GM);

					std::ostringstream strm;

					strm << "你在副本：【" << GetMapNameById(mapid) << "】竞速活动中获得第【" << rank << "】名，请查收你的奖励！";

					MailDraft draft("排行榜奖励", strm.str());

					SQLTransaction trans = CharacterDatabase.BeginTransaction();

					uint32 stack = pProto->Stackable > 0 ? pProto->Stackable : 1;

					while (itemcount > stack)
					{
						if (Item* item = Item::CreateItem(itemid, stack))
						{
							item->SaveToDB(trans);
							draft.AddItem(item);
						}
						itemcount -= stack;
					}

					if (Item* item = Item::CreateItem(itemid, itemcount))
					{
						item->SaveToDB(trans);
						draft.AddItem(item);
					}

					draft.SendMailTo(trans, MailReceiver(NULL, player->GetGUIDLow()), sender);
					CharacterDatabase.CommitTransaction(trans);

					//sLog->outString("发放【%u - %u - %u】排行榜奖励：地图ID：【%u】，地图：【%s】，玩家GUID：【%u】，玩家名字：【%s】，奖励物品ID：【%u】，数量：【%u】",year, month,day, mapid, GetMapNameById(mapid).c_str(), itr->playerguid, player->GetName().c_str(), itemid, itemcount);

					std::ostringstream rews;

					rews << itemid << "," << itemcount;

					//REPLACE INTO `_副本_击杀boss发奖记录表` (`玩家GUID`, `地图ID`, `名次`, `日期`, `奖励物品列表`, `是否发放奖励`) VALUE (?, ?, ?, NOW(), ?, ?)
					PreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_KILL_BOSS_RANK_SENDREW_LOGS);

					stmt->setUInt32(0, guidlow);
					stmt->setUInt32(1, mapid);
					stmt->setUInt32(2, rank);
					stmt->setString(3, rews.str());	//奖励物品列表
					stmt->setUInt32(4, 1);

					CharacterDatabase.Execute(stmt);

					itr->IsSendRew = true;
				}
			}
		}
	}
}

bool InstanceRankings::IsSendRewardsTime()
{
	time_t t = time(NULL);
	tm localTime;
	localtime_s(&localTime, &t);

	int year = localTime.tm_year + 1900;
	int month = localTime.tm_mon + 1;
	int day = localTime.tm_mday;
	int hour = localTime.tm_hour;
	int min = localTime.tm_min;
	int sec = localTime.tm_sec;
	int wday = localTime.tm_wday;

	for (auto & itr : rewDateLogVec)
	{
		//sLog->outString("001IsSendRewardsTime %u %u %u", year, month, day);
		//sLog->outString("002IsSendRewardsTime %u %u %u %u", itr.rewYear, itr.rewMon, itr.rewDay, itr.IsLogs ? 1 : 0);
		if (itr.rewYear == year && itr.rewMon == month && itr.rewDay == day && itr.IsLogs)
			return false;
	}

	// 检查小时是否大于6，或者小时等于6但分钟或秒数不为0
	return (hour > 6) ||
		(hour == 6 && (min > 0 || sec > 0));
}

void InstanceRankings::HandleSendKillBossTimerDataToAllPlayer(uint32 mapid)
{
	for (auto itr = sWorld->GetAllSessions().begin(); itr != sWorld->GetAllSessions().end(); ++itr)
	{
		if (Player * player = itr->second->GetPlayer())
		{
			SendKillBossTimeData(player, mapid);
		}
	}
}

void InstanceRankings::SendPlayerOwnerKillBossTimeAllData(Player * player)
{
	for (std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.begin(); itr != InstanceKillBossRanksMap.end(); ++itr)
	{
		SendPlayerOwnerKillBossTimeData(player, itr->first);
	}
}

void InstanceRankings::SendPlayerOwnerKillBossTimeData(Player * player, uint32 mapid)
{
	uint32 guidlow = player->GetGUIDLow();

	std::string chars = "#";

	std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.find(mapid);

	if (itr == PlayerKillBossTotalTimesMap.end())
		return;

	for (std::vector<PlayerKillBossTotalTimes>::iterator ite = itr->second.begin(); ite != itr->second.end(); ++ite)
	{
		if (ite->playerGuid == guidlow)
		{
			std::ostringstream RankData;
			RankData << mapid << chars << ite->rank << chars << ite->TotalTime;
			sGCAddon->SendPacketTo(player, "KILLBOSSOWNER", RankData.str());
			//sLog->outString("KILLBOSSOWNER | 玩家本人数据 | 玩家：【%s】| 数据：%s", player->GetName().c_str(), RankData.str().c_str());
			return;
		}
	}
}

void InstanceRankings::SendKillBossTimeData(Player * player, uint32 mapid)
{
	uint32 showRank = sSwitch->GetValue(ST_副本竞速排行榜显示最大名次) ? sSwitch->GetValue(ST_副本竞速排行榜显示最大名次) : 30;

	std::string chars = "#";

	std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.find(mapid);

	if (itr == PlayerKillBossTotalTimesMap.end())
		return;

	for (std::vector<PlayerKillBossTotalTimes>::iterator ite = itr->second.begin(); ite != itr->second.end(); ++ite)
	{
		if (ite->rank > 0 && ite->TotalTime != TimerInitValue && ite->TotalTime > 5)
		{
			if (ite->rank == 0 || (showRank > 0 && showRank < ite->rank))
				continue;

			std::ostringstream RankData;
			//			地图ID					名次						玩家姓名								总用时
			RankData << mapid << chars << ite->rank << chars << GetPlayerName(ite->playerGuid) << chars << ite->TotalTime;
			sGCAddon->SendPacketTo(player, "KILLBOSSRANK", RankData.str());
			//sLog->outString("KILLBOSSRANK | 排行榜数据（地图） | 数据：%s", RankData.str().c_str());
		}
	}
}

void InstanceRankings::SendAllKillBossTimeData(Player * player)
{
	uint32 showRank = sSwitch->GetValue(ST_副本竞速排行榜显示最大名次) ? sSwitch->GetValue(ST_副本竞速排行榜显示最大名次) : 30;

	std::string chars = "#";

	for (std::map<uint32/*mapid*/, std::vector<PlayerKillBossTotalTimes>>::iterator itr = PlayerKillBossTotalTimesMap.begin(); itr != PlayerKillBossTotalTimesMap.end(); ++itr)
	{
		uint32 mapid = itr->first;

		for (std::vector<PlayerKillBossTotalTimes>::iterator ite = itr->second.begin(); ite != itr->second.end(); ++ite)
		{
			if (ite->rank > 0 && ite->TotalTime != TimerInitValue && ite->TotalTime > 5)
			{
				if (ite->rank == 0 || (showRank > 0 && showRank < ite->rank))
					continue;

				std::ostringstream RankData;
				RankData << mapid << chars << ite->rank << chars << GetPlayerName(ite->playerGuid) << chars << ite->TotalTime;
				sGCAddon->SendPacketTo(player, "KILLBOSSALLRANK", RankData.str());
				//sLog->outString("KILLBOSSALLRANK | 排行榜数据（所有） | 数据：%s", RankData.str().c_str());
			}
		}

	}
}

void InstanceRankings::SendKillBossTemplateData(Player * player)
{
	std::string chars = ",";

	uint32 showRank = sSwitch->GetValue(ST_副本竞速排行榜显示最大名次) ? sSwitch->GetValue(ST_副本竞速排行榜显示最大名次) : 30;
	sGCAddon->SendPacketTo(player, "KILLBOSSSHOWMAX", std::to_string(showRank));
	//sLog->outString("KILLBOSSSHOWMAX | 排行榜配置数据 | 最多显示【%u】名次 ", showRank);

	std::string GeneralRew = sSwitch->GetFlagByIndex(ST_副本竞速通用奖励, 1) + "#" + sSwitch->GetFlagByIndex(ST_副本竞速通用奖励, 2);
	sGCAddon->SendPacketTo(player, "KILLBOSSGENREW", GeneralRew);
	//sLog->outString("KILLBOSSGENREW | 排行榜配置数据 | 通用奖励【%s】 ", GeneralRew.c_str());

	for (std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.begin(); itr != InstanceKillBossRanksMap.end(); ++itr)
	{
		uint32 mapid = itr->first;

		std::ostringstream rewData;

		rewData << mapid << "#";
			 
		for (auto &ite : itr->second.RankRewData)
			rewData << ite.first << chars << ite.second.itemid << chars << ite.second.count << chars;
		
		std::string msg = rewData.str();
		msg.pop_back();

		sGCAddon->SendPacketTo(player, "KILLBOSSREWDATA", msg);
		//sLog->outString("KILLBOSSREWDATA | 排行榜配置数据（所有） | 数据：%s", msg.c_str());
	}
}

void InstanceRankings::SendKillBossTempAllMapIdData(Player * player)
{
	if (InstanceKillBossRanksMap.empty())
		return;

	std::string chars = ",";
	std::ostringstream mapIds;
	for (std::map<uint32, InstanceKillBossRanksTemplate>::iterator itr = InstanceKillBossRanksMap.begin(); itr != InstanceKillBossRanksMap.end(); ++itr)
	{
		mapIds << itr->first << chars;
	}

	std::string msg = mapIds.str();
	if (msg.empty())
		return;

	msg.pop_back();

	sGCAddon->SendPacketTo(player, "KILLBOSSMAPIDS", msg);
	//sLog->outString("KILLBOSSMAPIDS | 排行榜配置数据 | 发送所有地图ID【%s】 ", msg.c_str());
}
