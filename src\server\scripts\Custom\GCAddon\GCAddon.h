﻿class GCAddon
{
public:
	static GCAddon* instance()
	{
		static GCAddon instance;
		return &instance;
	}

	void SendPacketTo(const Player* player, std::string opcode, std::string msg)
	{
		msg = opcode + "\t" + msg;
		WorldPacket data(SMSG_MESSAGECHAT, 100);
		data << uint8(CHAT_MSG_WHISPER);
		data << int32(LANG_ADDON);
		data << uint64(player->GetGUID());
		data << uint32(0);
		data << uint64(player->GetGUID());
		data << uint32(msg.length() + 1);
		data << msg;
		data << uint8(0);
		player->GetSession()->SendPacket(&data);
	}

	std::string SplitStr(std::string msg, uint32 index);

	bool OnRecv(Player* player, std::string msg);

	void SendAllData(Player* player);
	void SendReqData(Player* player);
	void SendRewData(Player* player);
	void SendItemData(Player* player, uint32 entry);
	void SendItemEnchantInfo(Item* item, Player* pl);

	//char data
	void SendCharData(Player* player);

	//发送弹窗消息
	void SendDualEventData(Player* player, uint32 eventid);

	bool isNumber(const std::string& msg);
	bool IsPositiveInteger(const std::string& str);
	bool SafeStringToInt(const std::string& str, uint32& result);
private:

};
#define sGCAddon GCAddon::instance()