﻿
#include "botcommon.h"

#define BOT_法宝_插槽最大值 5

struct BotExpFaBaoData
{
	friend class bot_exp;

public:
	uint32 owner;
	uint32 botclass;

	uint32 fabao[BOT_法宝_插槽最大值];
	//uint32 fabaoguid;
	//uint32 fabao_fm[MAX_ENCHANTMENT_SLOT];
private:

	explicit BotExpFaBaoData(uint32 _owner, uint32 _botguid) : owner(_owner), botclass(_botguid)
	{
		for (uint8 i = 0; i != BOT_法宝_插槽最大值; ++i)
			fabao[i] = 0;
	}

	BotExpFaBaoData(BotExpFaBaoData const&);
};

struct BotExpFaBaoTemp
{
	uint32 itemid;
	uint32 displayid;
	uint32 slotid;
};

enum BotExpValues
{
	BOT_TEXT_扩展_可用法宝			= 70900,
	BOT_TEXT_扩展_返回,
	BOT_TEXT_扩展_取下法宝,
	BOT_TEXT_扩展_未装备法宝,
	BOT_TEXT_扩展_当前法宝,
	BOT_TEXT_扩展_战士法宝,
	BOT_TEXT_扩展_圣骑法宝,
	BOT_TEXT_扩展_猎人法宝,
	BOT_TEXT_扩展_盗贼法宝,
	BOT_TEXT_扩展_牧师法宝,
	BOT_TEXT_扩展_死骑法宝,
	BOT_TEXT_扩展_萨满法宝,
	BOT_TEXT_扩展_法师法宝,
	BOT_TEXT_扩展_术士法宝,
	BOT_TEXT_扩展_德鲁伊法宝,
	BOT_TEXT_扩展_最大值,

	BOT_TEXT_扩展_法宝_槽位				= 71000,
	BOT_TEXT_扩展_法宝_槽位1_战士,		
	BOT_TEXT_扩展_法宝_槽位2_战士,
	BOT_TEXT_扩展_法宝_槽位3_战士,
	BOT_TEXT_扩展_法宝_槽位4_战士,
	BOT_TEXT_扩展_法宝_槽位5_战士,
	BOT_TEXT_扩展_法宝_槽位1_圣骑,
	BOT_TEXT_扩展_法宝_槽位2_圣骑,
	BOT_TEXT_扩展_法宝_槽位3_圣骑,
	BOT_TEXT_扩展_法宝_槽位4_圣骑,
	BOT_TEXT_扩展_法宝_槽位5_圣骑,
	BOT_TEXT_扩展_法宝_槽位1_猎人,
	BOT_TEXT_扩展_法宝_槽位2_猎人,
	BOT_TEXT_扩展_法宝_槽位3_猎人,
	BOT_TEXT_扩展_法宝_槽位4_猎人,
	BOT_TEXT_扩展_法宝_槽位5_猎人,
	BOT_TEXT_扩展_法宝_槽位1_盗贼,
	BOT_TEXT_扩展_法宝_槽位2_盗贼,
	BOT_TEXT_扩展_法宝_槽位3_盗贼,
	BOT_TEXT_扩展_法宝_槽位4_盗贼,
	BOT_TEXT_扩展_法宝_槽位5_盗贼,
	BOT_TEXT_扩展_法宝_槽位1_牧师,
	BOT_TEXT_扩展_法宝_槽位2_牧师,
	BOT_TEXT_扩展_法宝_槽位3_牧师,
	BOT_TEXT_扩展_法宝_槽位4_牧师,
	BOT_TEXT_扩展_法宝_槽位5_牧师,
	BOT_TEXT_扩展_法宝_槽位1_死骑,
	BOT_TEXT_扩展_法宝_槽位2_死骑,
	BOT_TEXT_扩展_法宝_槽位3_死骑,
	BOT_TEXT_扩展_法宝_槽位4_死骑,
	BOT_TEXT_扩展_法宝_槽位5_死骑,
	BOT_TEXT_扩展_法宝_槽位1_萨满,
	BOT_TEXT_扩展_法宝_槽位2_萨满,
	BOT_TEXT_扩展_法宝_槽位3_萨满,
	BOT_TEXT_扩展_法宝_槽位4_萨满,
	BOT_TEXT_扩展_法宝_槽位5_萨满,
	BOT_TEXT_扩展_法宝_槽位1_法师,
	BOT_TEXT_扩展_法宝_槽位2_法师,
	BOT_TEXT_扩展_法宝_槽位3_法师,
	BOT_TEXT_扩展_法宝_槽位4_法师,
	BOT_TEXT_扩展_法宝_槽位5_法师,
	BOT_TEXT_扩展_法宝_槽位1_术士,
	BOT_TEXT_扩展_法宝_槽位2_术士,
	BOT_TEXT_扩展_法宝_槽位3_术士,
	BOT_TEXT_扩展_法宝_槽位4_术士,
	BOT_TEXT_扩展_法宝_槽位5_术士,
	BOT_TEXT_扩展_法宝_槽位1_德鲁伊,
	BOT_TEXT_扩展_法宝_槽位2_德鲁伊,
	BOT_TEXT_扩展_法宝_槽位3_德鲁伊,
	BOT_TEXT_扩展_法宝_槽位4_德鲁伊,
	BOT_TEXT_扩展_法宝_槽位5_德鲁伊,
	BOT_TEXT_扩展_法宝_槽位_MAX,

	BOT_TEXT_扩展_法宝_槽位_错误	=	72000,

	GOSSIP_SENDER_扩展_法宝管理			= 100000,
	GOSSIP_SENDER_扩展_法宝管理_战士		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_WARRIOR,
	GOSSIP_SENDER_扩展_法宝管理_圣骑		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_PALADIN,
	GOSSIP_SENDER_扩展_法宝管理_猎人		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_HUNTER,
	GOSSIP_SENDER_扩展_法宝管理_盗贼		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_ROGUE,
	GOSSIP_SENDER_扩展_法宝管理_牧师		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_PRIEST,
	GOSSIP_SENDER_扩展_法宝管理_死骑		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_DEATH_KNIGHT,
	GOSSIP_SENDER_扩展_法宝管理_萨满		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_SHAMAN,
	GOSSIP_SENDER_扩展_法宝管理_法师		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_MAGE,
	GOSSIP_SENDER_扩展_法宝管理_术士		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_WARLOCK,
	GOSSIP_SENDER_扩展_法宝管理_德鲁伊		= GOSSIP_SENDER_扩展_法宝管理 + CLASS_DRUID,
	GOSSIP_SENDER_扩展_法宝管理_最大值,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_战士		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_WARRIOR,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_圣骑		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_PALADIN,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_猎人		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_HUNTER,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_盗贼		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_ROGUE,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_牧师		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_PRIEST,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_死骑		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_DEATH_KNIGHT,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_萨满		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_SHAMAN,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_法师		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_MAGE,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_术士		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_WARLOCK,
	GOSSIP_SENDER_扩展_法宝管理_取下法宝_德鲁伊		= GOSSIP_SENDER_扩展_法宝管理_取下法宝 + CLASS_DRUID,

	GOSSIP_SENDER_扩展_装备法宝			= 200000,
	GOSSIP_SENDER_扩展_装备法宝_战士		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_WARRIOR,
	GOSSIP_SENDER_扩展_装备法宝_战士_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_WARRIOR + BOT_法宝_插槽最大值 * 1,
	GOSSIP_SENDER_扩展_装备法宝_圣骑		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_PALADIN + BOT_法宝_插槽最大值 * 1,
	GOSSIP_SENDER_扩展_装备法宝_圣骑_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_PALADIN + BOT_法宝_插槽最大值 * 2,
	GOSSIP_SENDER_扩展_装备法宝_猎人		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_HUNTER+ BOT_法宝_插槽最大值 * 2,
	GOSSIP_SENDER_扩展_装备法宝_猎人_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_HUNTER+ BOT_法宝_插槽最大值 * 3,
	GOSSIP_SENDER_扩展_装备法宝_盗贼		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_ROGUE+ BOT_法宝_插槽最大值 * 3,
	GOSSIP_SENDER_扩展_装备法宝_盗贼_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_ROGUE+ BOT_法宝_插槽最大值 * 4,
	GOSSIP_SENDER_扩展_装备法宝_牧师		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_PRIEST+ BOT_法宝_插槽最大值 * 4,
	GOSSIP_SENDER_扩展_装备法宝_牧师_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_PRIEST+ BOT_法宝_插槽最大值 * 5,
	GOSSIP_SENDER_扩展_装备法宝_死骑		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_DEATH_KNIGHT+ BOT_法宝_插槽最大值 * 5,
	GOSSIP_SENDER_扩展_装备法宝_死骑_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_DEATH_KNIGHT+ BOT_法宝_插槽最大值 * 6,
	GOSSIP_SENDER_扩展_装备法宝_萨满		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_SHAMAN+ BOT_法宝_插槽最大值 * 6,
	GOSSIP_SENDER_扩展_装备法宝_萨满_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_SHAMAN+ BOT_法宝_插槽最大值 * 7,
	GOSSIP_SENDER_扩展_装备法宝_法师		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_MAGE+ BOT_法宝_插槽最大值 * 7,
	GOSSIP_SENDER_扩展_装备法宝_法师_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_MAGE+ BOT_法宝_插槽最大值 * 8,
	GOSSIP_SENDER_扩展_装备法宝_术士		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_WARLOCK+ BOT_法宝_插槽最大值 * 8,
	GOSSIP_SENDER_扩展_装备法宝_术士_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_WARLOCK+ BOT_法宝_插槽最大值 * 9,
	GOSSIP_SENDER_扩展_装备法宝_德鲁伊		= GOSSIP_SENDER_扩展_装备法宝 + CLASS_DRUID+ BOT_法宝_插槽最大值 * 9,
	GOSSIP_SENDER_扩展_装备法宝_德鲁伊_MAX	= GOSSIP_SENDER_扩展_装备法宝 + CLASS_DRUID+ BOT_法宝_插槽最大值 * 10,
	GOSSIP_SENDER_扩展_装备法宝_最大值,

	GOSSIP_SENDER_扩展_法宝管理界面		= 300000,
	GOSSIP_SENDER_扩展_法宝管理界面_战士法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_圣骑法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_猎人法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_盗贼法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_牧师法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_死骑法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_萨满法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_法师法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_术士法宝,
	GOSSIP_SENDER_扩展_法宝管理界面_德鲁伊法宝 = GOSSIP_SENDER_扩展_法宝管理界面 + CLASS_DRUID,
	GOSSIP_SENDER_扩展_法宝管理界面_最大值,

	GOSSIP_SENDER_扩展_返回法宝管理	= 9666666,
	GOSSIP_SENDER_扩展_返回主菜单		= 9777777,
	GOSSIP_SENDER_扩展_关闭对话		= 9999999,
};

class bot_exp
{
private:
	bot_exp();
	~bot_exp();

public:
	static bot_exp* instance()
	{
		static bot_exp instance;
		return &instance;
	}
	void Load();
	void LoadLog();

	static const std::string& LocalizedNpcText(uint32 textId);
	static BotExpFaBaoData const * SelectNpcBotFaBaoData(uint32 owner, uint32 botclass);
	void SendAllNpcBotFaBaoData(Player * player);
	void SendNpcBotFaBaoData(Player * player, uint32 botclass, uint32 slot, uint32 itemid, bool isEquip);
	void EquipFaBao(Player * player, uint32 botclass, uint32 slot, uint32 itemid);
	void UnEquipFaBao(Player * player, uint32 botclass, uint32 slot);

	uint32 * GetFaBaoId(uint32 owner, uint32 botclass);
	uint32 GetDisplayidFromFaBao(uint32 itemid);
	uint32 GetSlotidFromFaBao(uint32 itemid);
	uint32 GetGossipSenderVal(Classes botclass);

	void SaveDB(Player * player);
	void CreateBotExpFaBaoData(Player * player);
	void AddGossipItemFor(Player* player, uint32 icon, const std::string& text, uint32 sender, uint32 action);

	bool CanEquipFaBao(uint32 itemid, uint32 botclass, uint32 slot);
	bool GenerateFaBaoMgrGossip(Player * player, Creature * creature, uint32 sender, uint32 action);
	bool IsNpcBotFaBao(uint32 itemid);
	bool OnGossipHello(Player * player, Creature * creature);
	bool OnGossipSelect(Player* player, Creature* creature, uint32 sender, uint32 action);
};

#define sbotexp bot_exp::instance()