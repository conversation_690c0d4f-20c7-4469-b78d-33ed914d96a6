﻿#pragma execution_character_set("utf-8")
#include "CharMod.h"
#include "../Custom/FunctionCollection/FunctionCollection.h"
#include "../Custom/Switch/Switch.h"
#include "../Custom/Requirement/Requirement.h"
#include "../Custom/CommonFunc/CommonFunc.h"
#include "../Custom/AuthCheck/AuthCheck.h"
#include "../AI/NpcBots/botmgr.h"
#include "AccountMgr.h"
#include "../../PromoCode/PromoCode.h"

UNORDERED_MAP<uint8, CharStatTemplate> CharStatMap;
std::vector<uint32> AltClassSpellVec;
UNORDERED_MAP<uint32, uint32> XPReqMap;
UNORDERED_MAP<uint8, float> CharCombatRatingMap;
std::vector<NpcbotStatTemplate> NpcbotStatTemplateVec;
std::vector<ItemEffectItemForLoot> ItemEffectItemForLootVec;
std::vector<ClassesStatsModForMap> ClassesStatsModForMapVec;
std::vector<NpcbotIdStatTemplate> NpcbotIdStatTemplateVec;
std::vector<RexTemp> RexTempVec;
void CharMod::Load()
{
	CharStatMap.clear();
	uint32 count = 0;
	QueryResult result = WorldDatabase.PQuery(
		//		0			1			2			3			4			5			6				7			8
		"SELECT 职业索引,物理伤害倍率,法术伤害倍率,治疗效果倍率,护甲值上限,躲闪百分比上限,招架百分比上限,格挡百分比上限,爆击百分比上限,"
		//		9			10				11			12				13				14				15				16			17				18				19				20			21		
		"额外精神转法伤, 额外精神转治疗, 额外智力转法伤, 额外智力转治疗, 额外力量转攻强, 额外敏捷转攻强, 额外精神转攻强, 额外智力转攻强, 额外力量转法伤, 额外敏捷转法伤, 额外力量转治疗, 额外敏捷转治疗,减伤百分比,"
		//	22			23			24			25			26			27			28			29			30
		"主手攻速上限,副手攻速上限,远程攻速上限,主手伤害上限,副手伤害上限,远程伤害上限,韧性值上限,PVP伤害倍率,PVE伤害倍率,"
		// 31				32					33				34			35				36			37		38
		"智力转法术爆击倍率,敏捷转物理爆击倍率,敏捷转躲闪倍率,敏捷转护甲倍率,力量转格挡倍率,治疗量上限,生命值上限,法力值上限 FROM _属性调整_职业");

	if (result)
	{
		do
		{
			Field* fields = result->Fetch();
			CharStatTemplate CharStatTemp;
			CharStatTemp.meleeDmgMod	= fields[1].GetFloat();
			CharStatTemp.spellDmgMod	= fields[2].GetFloat();
			CharStatTemp.healMod		= fields[3].GetFloat();
			CharStatTemp.armorLimit		= fields[4].GetFloat();
			CharStatTemp.dodgeLimit		= fields[5].GetFloat();
			CharStatTemp.parryLimit		= fields[6].GetFloat();
			CharStatTemp.blockLimit		= fields[7].GetFloat();
			CharStatTemp.critLimit		= fields[8].GetFloat();
			CharStatTemp.spirit2SP		= fields[9].GetFloat();
			CharStatTemp.spirit2Heal	= fields[10].GetFloat();
			CharStatTemp.intellect2SP	= fields[11].GetFloat();
			CharStatTemp.intellect2Heal = fields[12].GetFloat();
			CharStatTemp.strength2AP	= fields[13].GetFloat();
			CharStatTemp.agility2AP		= fields[14].GetFloat();

			CharStatTemp.spirit2AP		= fields[15].GetFloat();
			CharStatTemp.intellect2AP	= fields[16].GetFloat();
			CharStatTemp.strength2SP	= fields[17].GetFloat();
			CharStatTemp.agility2SP		= fields[18].GetFloat();
			CharStatTemp.strength2Heal	= fields[19].GetFloat();
			CharStatTemp.agility2Heal	= fields[20].GetFloat();

			CharStatTemp.reduceDmgMod	= fields[21].GetFloat();

			for (size_t i = 0; i < 3; i++)
				CharStatTemp.attackSpeedLimit[i] = fields[22 + i].GetFloat() * 1000;

			for (size_t i = 0; i < 3; i++)
				CharStatTemp.dmgLimit[i] = fields[25 + i].GetFloat();

			CharStatTemp.resilienceLimit = fields[28].GetFloat();
			CharStatTemp.pvpDmgMod		 = fields[29].GetFloat();
			CharStatTemp.pveDmgMod       = fields[30].GetFloat();

			CharStatTemp.intellect2SpellCritMod = fields[31].GetFloat();
			CharStatTemp.agility2MeleeCritMod	= fields[32].GetFloat();
			CharStatTemp.agility2DodgeMod		= fields[33].GetFloat();
			CharStatTemp.agility2ArmmorMod		= fields[34].GetFloat();
			CharStatTemp.stength2BlockMod		= fields[35].GetFloat();
			CharStatTemp.healLimit				= fields[36].GetFloat();
			CharStatTemp.hpLimit				= fields[37].GetFloat();
			CharStatTemp.manaLimit				= fields[38].GetFloat();

			CharStatMap.insert(std::make_pair(fields[0].GetUInt8(), CharStatTemp));
			count++;
		} while (result->NextRow());
	}
	LoadDBLog("_属性调整_职业", count);

	CharCombatRatingMap.clear();

	if (AuthFunc(AF_COMBAT_RATING_MOD))
	{
		count = 0;
		if (result = WorldDatabase.PQuery(
			"SELECT 类型,倍率 FROM _属性调整_玩家属性倍率_x"))
		{
			do
			{
				Field* fields = result->Fetch();
				uint8 cr = fields[0].GetUInt8();
				float muil = fields[1].GetFloat();

				if (cr == 14)
				{
					CharCombatRatingMap.insert(std::make_pair(14, muil));
					CharCombatRatingMap.insert(std::make_pair(15, muil));
					CharCombatRatingMap.insert(std::make_pair(16, muil));
				}
				else
					CharCombatRatingMap.insert(std::make_pair(cr, muil));

				count++;
			} while (result->NextRow());
		}
		LoadDBLog("_属性调整_玩家属性倍率_x", count);
	}
	
	NpcbotStatTemplateVec.clear();
	if (result = WorldDatabase.PQuery("SELECT 职业索引,额外力量转攻强,额外敏捷转攻强,额外智力转攻强,额外精神转攻强,额外精神转法伤,额外智力转法伤,额外力量转法伤,额外敏捷转法伤,额外精神转治疗,额外智力转治疗,额外力量转治疗,额外敏捷转治疗,急速倍率 FROM _佣兵_属性调整"))
	{
		count = 0;
		do
		{
			Field* fields = result->Fetch();
			NpcbotStatTemplate Temp;
			uint32 index = 0;
			Temp.classIndex = fields[index].GetUInt32();

			Temp.strength2AP = fields[++index].GetFloat();
			Temp.agility2AP = fields[++index].GetFloat();
			Temp.intellect2AP = fields[++index].GetFloat();
			Temp.spirit2AP = fields[++index].GetFloat();

			Temp.spirit2SP = fields[++index].GetFloat();
			Temp.intellect2SP = fields[++index].GetFloat();
			Temp.strength2SP = fields[++index].GetFloat();
			Temp.agility2SP = fields[++index].GetFloat();

			Temp.spirit2Heal = fields[++index].GetFloat();
			Temp.intellect2Heal = fields[++index].GetFloat();
			Temp.strength2Heal = fields[++index].GetFloat();
			Temp.agility2Heal = fields[++index].GetFloat();

			Temp.hastePct = fields[++index].GetFloat();

			NpcbotStatTemplateVec.push_back(Temp);
			++count;
		} while (result->NextRow());
	}
	LoadDBLog("_佣兵_属性调整", count);

	_PlayerNpcbotNum.clear();
	if (result = WorldDatabase.PQuery("SELECT 账号ID,最大佣兵数量 FROM _佣兵_玩家记录表"))
	{
		do
		{
			Field* fields = result->Fetch();
			uint32 aid = fields[0].GetUInt32();
			uint32 val = fields[1].GetUInt32();

			_PlayerNpcbotNum[aid] = val;
		} while (result->NextRow());
	}

	ClassesStatsModForMapVec.clear();
	if (result = WorldDatabase.PQuery("SELECT 职业ID, 地图ID, 区域ID, 地域ID, 生命值min, 生命值max, 力量min, 力量max, 耐力min, 耐力max, 敏捷min, 敏捷max, 智慧min, 智慧max, 精神min, 精神max, 攻强min, 攻强max, 法强min, 法强max, 急速min, 急速max, 护甲min, 护甲max, 韧性min, 韧性max, 暴击min, 暴击max, 命中min, 命中max, 闪躲min, 闪躲max, 招架min, 招架max, 格挡min, 格挡max, 防御等级min, 防御等级max FROM _职业属性调整_地图"))
	{
		do
		{
			Field* fields = result->Fetch();
			ClassesStatsModForMap Temp;
			Temp.class_id = fields[0].GetUInt32();
			Temp.map_id = fields[1].GetInt32();
			Temp.zone_id = fields[2].GetUInt32();
			Temp.area_id = fields[3].GetUInt32();
			Temp.hp_min = fields[4].GetUInt32();
			Temp.hp_max = fields[5].GetUInt32();
			Temp.strength_min = fields[6].GetUInt32();
			Temp.strength_max = fields[7].GetUInt32();
			Temp.stamina_min = fields[8].GetUInt32();
			Temp.stamina_max = fields[9].GetUInt32();
			Temp.agility_min = fields[10].GetUInt32();
			Temp.agility_max = fields[11].GetUInt32();
			Temp.intellect_min = fields[12].GetUInt32();
			Temp.intellect_max = fields[13].GetUInt32();
			Temp.spirit_min = fields[14].GetUInt32();
			Temp.spirit_max = fields[15].GetUInt32();
			Temp.attack_power_min = fields[16].GetUInt32();
			Temp.attack_power_max = fields[17].GetUInt32();
			Temp.spell_power_min = fields[18].GetUInt32();
			Temp.spell_power_max = fields[19].GetUInt32();
			Temp.haste_min = fields[20].GetUInt32();
			Temp.haste_max = fields[21].GetUInt32();
			Temp.armor_min = fields[22].GetUInt32();
			Temp.armor_max = fields[23].GetUInt32();
			Temp.Resilience_min = fields[24].GetUInt32();
			Temp.Resilience_max = fields[25].GetUInt32();
			Temp.crit_min = fields[26].GetFloat();
			Temp.crit_max = fields[27].GetFloat();
			Temp.hit_min = fields[28].GetUInt32();
			Temp.hit_max = fields[29].GetUInt32();
			Temp.dodge_min = fields[30].GetFloat();
			Temp.dodge_max = fields[31].GetFloat();
			Temp.parry_min = fields[32].GetFloat();
			Temp.parry_max = fields[33].GetFloat();
			Temp.block_min = fields[34].GetFloat();
			Temp.block_max = fields[35].GetFloat();
			Temp.defense_min = fields[36].GetUInt32();
			Temp.defense_max = fields[37].GetUInt32();
			ClassesStatsModForMapVec.push_back(Temp);
		} while (result->NextRow());
	}

	ItemEffectItemForLootVec.clear();
	if (result = WorldDatabase.PQuery("SELECT 需求物品ID,倍数,拾取物品ID FROM _物品_多倍拾取"))
	{
		do
		{
			Field* fields = result->Fetch();
			ItemEffectItemForLoot Temp;
			Temp.Effect_Itemid = fields[0].GetUInt32();
			Temp.mult = fields[1].GetUInt32();
			Temp.Effected_Itemid = fields[2].GetUInt32();
			ItemEffectItemForLootVec.push_back(Temp);
		} while (result->NextRow());
	}

	NpcbotIdStatTemplateVec.clear();
	if (QueryResult results = WorldDatabase.PQuery("SELECT 生物编号,额外力量转攻强,额外敏捷转攻强,额外智力转攻强,额外精神转攻强,额外精神转法伤,额外智力转法伤,额外力量转法伤,额外敏捷转法伤,额外精神转治疗,额外智力转治疗,额外力量转治疗,额外敏捷转治疗,急速倍率 FROM _佣兵_属性调整_编号"))
	{
		do
		{
			Field* fields = results->Fetch();
			NpcbotIdStatTemplate Temp;
			uint32 index = 0;
			Temp.entry = fields[0].GetUInt32();
			
			Temp.strength2AP = fields[++index].GetFloat();
			Temp.agility2AP = fields[++index].GetFloat();
			Temp.intellect2AP = fields[++index].GetFloat();
			Temp.spirit2AP = fields[++index].GetFloat();

			Temp.spirit2SP = fields[++index].GetFloat();
			Temp.intellect2SP = fields[++index].GetFloat();
			Temp.strength2SP = fields[++index].GetFloat();
			Temp.agility2SP = fields[++index].GetFloat();

			Temp.spirit2Heal = fields[++index].GetFloat();
			Temp.intellect2Heal = fields[++index].GetFloat();
			Temp.strength2Heal = fields[++index].GetFloat();
			Temp.agility2Heal = fields[++index].GetFloat();

			Temp.hastePct = fields[++index].GetFloat();

			NpcbotIdStatTemplateVec.push_back(Temp);
		} while (results->NextRow());
	}

	RexTempVec.clear();
	if (result = WorldDatabase.PQuery("SELECT mapid, reqid FROM __自定义复活"))
	{
		do
		{
			Field* fields = result->Fetch();
			RexTemp td;
			td.mapid = fields[0].GetUInt32();
			td.reqid = fields[1].GetUInt32();

			RexTempVec.push_back(td);

		} while (result->NextRow());
	}

	SessionMap const& smap = sWorld->GetAllSessions();
	for (SessionMap::const_iterator i = smap.begin(); i != smap.end(); ++i)
		if (Player* player = i->second->GetPlayer())
		{
			ModLimit(player);
			if (BotMgr * bmgr = player->GetBotMgr())
				bmgr->SetBotMod();
		}
}

float CharMod::GetBotValue(SwitchStatTypes type, uint8 classIndex)
{
	uint32 len = NpcbotStatTemplateVec.size();
	for (size_t i = 0; i < len; i++)
	{
		if (classIndex == NpcbotStatTemplateVec[i].classIndex)
		{
			switch (type)
			{
			case SWITCH_SPIRIT2SP:
				return NpcbotStatTemplateVec[i].spirit2SP;
			case SWITCH_SPIRIT2HEAL:
				return NpcbotStatTemplateVec[i].spirit2Heal;
			case SWITCH_INTELLECT2SP:
				return NpcbotStatTemplateVec[i].intellect2SP;
			case SWITCH_INTELLECT2HEAL:
				return NpcbotStatTemplateVec[i].intellect2Heal;
			case SWITCH_STRENGTH2AP:
				return NpcbotStatTemplateVec[i].strength2AP;
			case SWITCH_AGILITY2AP:
				return NpcbotStatTemplateVec[i].agility2AP;
			case SWITCH_SPIRIT2AP:
				return  NpcbotStatTemplateVec[i].spirit2AP;
			case SWITCH_INTELLECT2AP:
				return  NpcbotStatTemplateVec[i].intellect2AP;
			case SWITCH_STRENGTH2SP:
				return  NpcbotStatTemplateVec[i].strength2SP;
			case SWITCH_AGILITY2SP:
				return  NpcbotStatTemplateVec[i].agility2SP;
			case SWITCH_STRENGTH2HEAL:
				return  NpcbotStatTemplateVec[i].strength2Heal;
			case SWITCH_AGILITY2HEAL:
				return  NpcbotStatTemplateVec[i].agility2Heal;
			case SWITCH_BOT_HASTE:
				return  NpcbotStatTemplateVec[i].hastePct;
			}
		}
	}
	return 0.0f;
}

float CharMod::GetBotValueForEntry(SwitchStatTypes type, uint32 entry)
{
	float val = 0.0f;
	for (std::vector<NpcbotIdStatTemplate>::iterator itr = NpcbotIdStatTemplateVec.begin(); itr != NpcbotIdStatTemplateVec.end(); ++itr)
	{
		if (entry == itr->entry)
		{			
			switch (type)
			{
			case SWITCH_SPIRIT2SP:
				val = itr->spirit2SP;
				break;
			case SWITCH_SPIRIT2HEAL:
				val = itr->spirit2Heal;
				break;
			case SWITCH_INTELLECT2SP:
				val = itr->intellect2SP;
				break;
			case SWITCH_INTELLECT2HEAL:
				val = itr->intellect2Heal;
				break;
			case SWITCH_STRENGTH2AP:
				val = itr->strength2AP;
				break;
			case SWITCH_AGILITY2AP:
				val = itr->agility2AP;
				break;
			case SWITCH_SPIRIT2AP:
				val = itr->spirit2AP;
				break;
			case SWITCH_INTELLECT2AP:
				val = itr->intellect2AP;
				break;
			case SWITCH_STRENGTH2SP:
				val = itr->strength2SP;
				break;
			case SWITCH_AGILITY2SP:
				val = itr->agility2SP;
				break;
			case SWITCH_STRENGTH2HEAL:
				val = itr->strength2Heal;
				break;
			case SWITCH_AGILITY2HEAL:
				val = itr->agility2Heal;
				break;
			case SWITCH_BOT_HASTE:
				val = itr->hastePct;
				break;
			default:
				break;
			}
		}
	}
	return val;
}

float CharMod::GetLimit(StatLimitTypes type, uint8 classIndex)
{
	auto itr = CharStatMap.find(classIndex);

	if (itr != CharStatMap.end())
	{
		switch (type)
		{
		case LIMIT_ARMOR:
			return itr->second.armorLimit;
		case LIMIT_DODGE:
			return itr->second.dodgeLimit;
		case LIMIT_PARRY:
			return itr->second.parryLimit;
		case LIMIT_BLOCK:
			return itr->second.blockLimit;
		case LIMIT_CRIT:
			return itr->second.critLimit;
		case LIMIT_RESILIENCE:
			return itr->second.resilienceLimit;
		case LIMIT_HP:
			return itr->second.hpLimit;
		case LIMIT_MANA:
			return itr->second.manaLimit;
		case LIMIT_HEAL:
			return itr->second.healLimit;
		default:
			return 0;
		}
	}

	return 0;
}

float CharMod::GetMod(StatModTypes type, uint8 classIndex)
{
	auto itr = CharStatMap.find(classIndex);

	if (itr != CharStatMap.end())
	{
		switch (type)
		{
		case CHAR_MOD_MELEE_DMG:
			return itr->second.meleeDmgMod;
		case CHAR_MOD_SPELL_DMG:
			return itr->second.spellDmgMod;
		case CHAR_MOD_HEAL:
			return itr->second.healMod;
		case CHAR_MOD_REDUCE_DMG:
			return itr->second.reduceDmgMod;
		case CHAR_MOD_PVP_DMG:
			return itr->second.pvpDmgMod;
		case CHAR_MOD_PVE_DMG:
			return itr->second.pveDmgMod;
		case CHAR_MOD_INTELLECT2SPELLCRIT:
			return itr->second.intellect2SpellCritMod;
		case CHAR_MOD_AGILITY2MELEECRIT:
			return itr->second.agility2MeleeCritMod;
		case CHAR_MOD_AGILITY2DODGE:
			return itr->second.agility2DodgeMod;
		case CHAR_MOD_AGILITY2ARMMOR:
			return itr->second.agility2ArmmorMod;
		case CHAR_MOD_STRENGTH2BLOCK:
			return itr->second.stength2BlockMod;
		default:
			return 1.0f;
		}
	}

	return 1.0f;
}

float CharMod::GetValue(SwitchStatTypes type, uint8 classIndex)
{
	auto itr = CharStatMap.find(classIndex);

	if (itr != CharStatMap.end())
	{
		switch (type)
		{
		case SWITCH_SPIRIT2SP:
			return itr->second.spirit2SP;
		case SWITCH_SPIRIT2HEAL:
			return itr->second.spirit2Heal;
		case SWITCH_INTELLECT2SP:
			return itr->second.intellect2SP;
		case SWITCH_INTELLECT2HEAL:
			return itr->second.intellect2Heal;
		case SWITCH_STRENGTH2AP:
			return itr->second.strength2AP;
		case SWITCH_AGILITY2AP:
			return itr->second.agility2AP;
		case SWITCH_SPIRIT2AP:
			return  itr->second.spirit2AP;
		case SWITCH_INTELLECT2AP:
			return  itr->second.intellect2AP;
		case SWITCH_STRENGTH2SP:
			return  itr->second.strength2SP;
		case SWITCH_AGILITY2SP:
			return  itr->second.agility2SP;
		case SWITCH_STRENGTH2HEAL:
			return  itr->second.strength2Heal;
		case SWITCH_AGILITY2HEAL:
			return  itr->second.agility2Heal;
		default:
			return 0.0f;
		}
	}

	return 0.0f;
}

int32 CharMod::GetExtraSP(Player* player)
{
	float intellectValue = player->GetTotalStatValue(Stats(STAT_INTELLECT));
	float spiritValue = player->GetTotalStatValue(Stats(STAT_SPIRIT));
	float strengthValue = player->GetTotalStatValue(Stats(STAT_STRENGTH));
	float agilityValue = player->GetTotalStatValue(Stats(STAT_AGILITY));

	return int32(player->p_sp + intellectValue * player->p_intellect2SP + spiritValue * player->p_spirit2SP + strengthValue * player->p_strength2SP + agilityValue * player->p_agility2SP);
}
int32 CharMod::GetExtraAP(Player* player)
{
	float strengthValue = player->GetTotalStatValue(Stats(STAT_STRENGTH));
	float agilityValue = player->GetTotalStatValue(Stats(STAT_AGILITY));
	float intellectValue = player->GetTotalStatValue(Stats(STAT_INTELLECT));
	float spiritValue = player->GetTotalStatValue(Stats(STAT_SPIRIT));

	return int32(player->p_ap + strengthValue * player->p_strength2AP + agilityValue * player->p_agility2AP + intellectValue * player->p_intellect2AP + spiritValue * player->p_spirit2AP);
}

int32 CharMod::GetExtraHeal(Player* player)
{
	float intellectValue = player->GetTotalStatValue(Stats(STAT_INTELLECT));
	float spiritValue = player->GetTotalStatValue(Stats(STAT_SPIRIT));
	float strengthValue = player->GetTotalStatValue(Stats(STAT_STRENGTH));
	float agilityValue = player->GetTotalStatValue(Stats(STAT_AGILITY));

	return int32(player->p_heal + intellectValue * player->p_intellect2Heal + spiritValue * player->p_spirit2Heal + strengthValue * player->p_strength2Heal + agilityValue * player->p_agility2Heal);
}

void CharMod::ModLimit(Player* player)
{
	player->p_intellect2SP = GetValue(SWITCH_INTELLECT2SP, player->getClass());
	player->p_spirit2SP = GetValue(SWITCH_SPIRIT2SP, player->getClass());
	player->p_intellect2Heal = GetValue(SWITCH_INTELLECT2HEAL, player->getClass());
	player->p_spirit2Heal = GetValue(SWITCH_SPIRIT2HEAL, player->getClass());
	player->p_strength2AP = GetValue(SWITCH_STRENGTH2AP, player->getClass());
	player->p_agility2AP = GetValue(SWITCH_AGILITY2AP, player->getClass());


	player->p_agility2SP = GetValue(SWITCH_AGILITY2SP, player->getClass());
	player->p_strength2SP = GetValue(SWITCH_STRENGTH2SP, player->getClass());
	player->p_intellect2AP = GetValue(SWITCH_INTELLECT2AP, player->getClass());
	player->p_spirit2AP = GetValue(SWITCH_SPIRIT2AP, player->getClass());
	player->p_agility2Heal = GetValue(SWITCH_AGILITY2HEAL, player->getClass());
	player->p_strength2Heal = GetValue(SWITCH_STRENGTH2HEAL, player->getClass());

	player->p_meleeDmgMod = GetMod(CHAR_MOD_MELEE_DMG, player->getClass());
	player->p_spellDmgMod = GetMod(CHAR_MOD_SPELL_DMG, player->getClass());
	player->p_healMod = GetMod(CHAR_MOD_HEAL, player->getClass());
	player->p_reduceDmgMod = GetMod(CHAR_MOD_REDUCE_DMG, player->getClass());

	player->p_armorLimit = GetLimit(LIMIT_ARMOR, player->getClass());
	player->p_dodgeLimit = GetLimit(LIMIT_DODGE, player->getClass());
	player->p_parryLimit = GetLimit(LIMIT_PARRY, player->getClass());
	player->p_blockLimit = GetLimit(LIMIT_BLOCK, player->getClass());
	player->p_critLimit = GetLimit(LIMIT_CRIT, player->getClass());
	player->p_resilienceLimit = GetLimit(LIMIT_RESILIENCE, player->getClass());


	auto itr = CharStatMap.find(player->getClass());

	if (itr != CharStatMap.end())
	{
		for (size_t i = 0; i < 3; i++)
			player->p_attackSpeedLimit[i] = itr->second.attackSpeedLimit[i];
		for (size_t i = 0; i < 3; i++)
			player->p_dmgLimit[i] = itr->second.dmgLimit[i];
	}

	player->SetRegularAttackTime();

	player->p_pvpdmgMod = GetMod(CHAR_MOD_PVP_DMG, player->getClass());
	player->p_pvedmgMod = GetMod(CHAR_MOD_PVE_DMG, player->getClass());


	player->p_intellect2SpellCritMod = GetMod(CHAR_MOD_INTELLECT2SPELLCRIT, player->getClass());
	player->p_agility2MeleeCritMod = GetMod(CHAR_MOD_AGILITY2MELEECRIT, player->getClass());
	player->p_agility2DodgeMod = GetMod(CHAR_MOD_AGILITY2DODGE, player->getClass());
	player->p_agility2ArmmorMod = GetMod(CHAR_MOD_AGILITY2ARMMOR, player->getClass());
	player->p_stength2BlockMod = GetMod(CHAR_MOD_STRENGTH2BLOCK, player->getClass());

	player->p_healLimit = GetLimit(LIMIT_HEAL, player->getClass());
	player->p_hpLimit = GetLimit(LIMIT_HP, player->getClass());
	player->p_manaLimit = GetLimit(LIMIT_MANA, player->getClass());

	player->UpdateAllStats();
}

bool CharMod::CheckFamily(Player* player, uint32 SpellFamily)
{
	switch (player->getClass())
	{
	case CLASS_WARRIOR:
		return SpellFamily == SPELLFAMILY_WARRIOR;
	case CLASS_PALADIN:
		return SpellFamily == SPELLFAMILY_PALADIN;
	case CLASS_HUNTER:
		return SpellFamily == SPELLFAMILY_HUNTER;
	case CLASS_ROGUE:
		return SpellFamily == SPELLFAMILY_ROGUE;
	case CLASS_PRIEST:
		return SpellFamily == SPELLFAMILY_PRIEST;
	case CLASS_DEATH_KNIGHT:
		return SpellFamily == SPELLFAMILY_DEATHKNIGHT;
	case CLASS_SHAMAN:
		return SpellFamily == SPELLFAMILY_SHAMAN;
	case CLASS_MAGE:
		return SpellFamily == SPELLFAMILY_MAGE;
	case CLASS_WARLOCK:
		return SpellFamily == SPELLFAMILY_WARLOCK;
	case CLASS_DRUID:
		return SpellFamily == SPELLFAMILY_DRUID;
	}

	return false;
}

bool CharMod::CheckSkill(SpellInfo const* spellInfo)
{
	return
		spellInfo->IsAbilityOfSkillType(SKILL_ALCHEMY) ||			//炼金
		spellInfo->IsAbilityOfSkillType(SKILL_BLACKSMITHING) ||		//锻造
		spellInfo->IsAbilityOfSkillType(SKILL_COOKING) ||			//烹饪
		spellInfo->IsAbilityOfSkillType(SKILL_ENCHANTING) ||		//附魔
		spellInfo->IsAbilityOfSkillType(SKILL_ENGINEERING) ||		//工程
		spellInfo->IsAbilityOfSkillType(SKILL_FIRST_AID) ||			//急救
		spellInfo->IsAbilityOfSkillType(SKILL_HERBALISM) ||			//草药
		spellInfo->IsAbilityOfSkillType(SKILL_LEATHERWORKING) ||	//制皮
		spellInfo->IsAbilityOfSkillType(SKILL_INSCRIPTION) ||		//铭文
		spellInfo->IsAbilityOfSkillType(SKILL_TAILORING) ||			//裁缝
		spellInfo->IsAbilityOfSkillType(SKILL_MINING) ||			//挖矿
		spellInfo->IsAbilityOfSkillType(SKILL_FISHING) ||			//钓鱼
		spellInfo->IsAbilityOfSkillType(SKILL_SKINNING) ||			//剥皮
		spellInfo->IsAbilityOfSkillType(SKILL_JEWELCRAFTING) ||		//珠宝
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_HORSE) ||		//骑术
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_WOLF) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_TIGER) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_RAM) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_RAPTOR) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_MECHANOSTRIDER) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_UNDEAD_HORSE) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING_KODO) ||
		spellInfo->IsAbilityOfSkillType(SKILL_RIDING) ||
		spellInfo->HasAura(SPELL_AURA_MOUNTED) ||					//坐骑
		spellInfo->IsAbilityOfSkillType(SKILL_COMPANIONS);			//小宠物
}

void CharMod::ModClass(Player* player, uint8 targetClass)
{
	player->CLOSE_GOSSIP_MENU();
	
	uint32 reqId = sSwitch->GetValue(ST_ALT_CLASS);

	if (!sReq->Check(player, reqId))
		return;

	sReq->Des(player, reqId);

	uint32 bytes0 = 0;
	bytes0 |= player->getRace(); 
	bytes0 |= targetClass << 8; 
	bytes0 |= player->getGender() << 16;

	ChrClassesEntry const* cEntry = sChrClassesStore.LookupEntry(targetClass);
	if (cEntry && cEntry->powerType < MAX_POWERS)
		bytes0 |= cEntry->powerType << 24;

	player->SetUInt32Value(UNIT_FIELD_BYTES_0, bytes0);

	PlayerSpellMap spellMap = player->GetSpellMap();

	for (PlayerSpellMap::const_iterator iter = spellMap.begin(); iter != spellMap.end(); ++iter)
		if (SpellInfo const* spellInfo = sSpellMgr->GetSpellInfo(iter->first))
			if(!CheckSkill(spellInfo))
				if (AltClassSpellVec.empty() || std::find(AltClassSpellVec.begin(), AltClassSpellVec.end(), iter->first) == AltClassSpellVec.end())
					player->removeSpell(iter->first, SPEC_MASK_ALL, false);

	player->learnDefaultSpells();
	player->learnQuestRewardedSpells();

	player->resetTalents(true);
	player->SendTalentsInfoData(false);


	for (uint32 id = 0; id < sSkillLineStore.GetNumRows(); id++)
	{

		SkillLineEntry const* pSkill = sSkillLineStore.LookupEntry(id);
		if (!pSkill)
			continue;

		if (pSkill->categoryId != SKILL_CATEGORY_CLASS)
			continue;

		player->SetSkill(id, 0, 0, 0);
	}

	player->CastSpell(player, 63680, true, NULL, NULL, player->GetGUID());

	if (targetClass == CLASS_DEATH_KNIGHT)
		player->InitRunes();

	//CharacterDatabase.PExecute("update characters set class=%u where guid =%u", targetClass, player->GetGUIDLow()); //更新转职后的职业
	CharacterDatabase.PExecute("delete from character_glyphs where guid =%u", player->GetGUIDLow()); // 删除雕文
	CharacterDatabase.PExecute("delete from character_pet where owner =%u", player->GetGUIDLow());//删除宠物

	//Customize(GetGUID(), getGender(), 1, 1, 1, 1, 1);
	sWorld->UpdateGlobalPlayerData(player->GetGUIDLow(), PLAYER_UPDATE_DATA_CLASS, "", 0, 0, 0, targetClass);
	player->GetSession()->KickPlayer();
}

std::string CharMod::GetClassName1(uint32 _class)
{
	switch (_class)
	{
	case CLASS_WARRIOR:
		return "[战士]";
	case CLASS_PALADIN:
		return "[圣骑士]";
	case CLASS_HUNTER:
		return "[猎人]";
	case CLASS_ROGUE:
		return "[盗贼]";
	case CLASS_PRIEST:
		return "[牧师]";
	case CLASS_DEATH_KNIGHT:
		return "[死亡骑士]";
	case CLASS_SHAMAN:
		return "[萨满祭司]";
	case CLASS_MAGE:
		return "[法师]";
	case CLASS_WARLOCK:
		return "[术士]";
	case CLASS_DRUID:
		return "[德鲁伊]";
	default:
		return "";
	}
}

void CharMod::AddGossip(Player* player, Object* obj)
{
	uint8 race = player->getRace();
	uint8 _class = player->getClass();
	uint32 classMaskDisabled = sWorld->getIntConfig(CONFIG_CHARACTER_CREATING_DISABLED_CLASSMASK);

	for (size_t i = CLASS_WARRIOR; i <= CLASS_DRUID; i++)
	{
		if (i == 10 || _class == i || ((1 << (_class - 1)) & classMaskDisabled))
			continue;

		PlayerInfo const* info = sObjectMgr->GetPlayerInfo(race, i);

		if (!info)
			continue;

		std::string name = GetClassName1(i);
		player->ADD_GOSSIP_ITEM_EXTENDED(0, "转职 -> " + name, SENDER_ALT_CLASS, i, sReq->Notice(player, sSwitch->GetValue(ST_ALT_CLASS), "转职为", name), 0, false);
	}

	if (obj->ToCreature())
		player->SEND_GOSSIP_MENU(obj->GetEntry(), obj->GetGUID());
	else
		player->SEND_GOSSIP_MENU(DEFAULT_GOSSIP_MESSAGE, obj->GetGUID());
}

void CharMod::InitPlayerBotNum(Player * player)
{
	uint32 aid = player->GetSession()->GetAccountId();

	QueryResult result = WorldDatabase.PQuery("SELECT 最大佣兵数量 FROM _佣兵_玩家记录表 WHERE 账号ID = %u", aid);

	if (result)
		return;

	if (!result)
		WorldDatabase.PExecute("INSERT INTO _佣兵_玩家记录表 (账号ID, 最大佣兵数量)VALUES(%u, %u)", aid, INIT_NPCBOT_NUM);

	_PlayerNpcbotNum[aid] = INIT_NPCBOT_NUM;
}

uint32 CharMod::GetPlayerBotNum(uint32 aid)
{
	auto itr = _PlayerNpcbotNum.find(aid);

	return itr != _PlayerNpcbotNum.end() ? itr->second : 0;
}

void CharMod::AddPlayerBotNum(uint32 aid, uint32 val)
{
	_PlayerNpcbotNum[aid] += val;
}

void CharMod::SavePlayerBotNum(uint32 aid)
{
	uint32 num = _PlayerNpcbotNum[aid];

	QueryResult result = WorldDatabase.PQuery("UPDATE _佣兵_玩家记录表 SET 最大佣兵数量 = %u WHERE 账号ID = %u", num, aid);
}

uint32 CharMod::GetItemLootMult(Player* player, uint32 lootitemid)
{
	for (std::vector<ItemEffectItemForLoot>::const_iterator itr = ItemEffectItemForLootVec.begin(); itr != ItemEffectItemForLootVec.end(); ++itr)
	{
		if (itr->Effected_Itemid == lootitemid)
			if (player->HasItemCount(itr->Effect_Itemid))
				return itr->mult;
	}

	return 1;
}

uint32 CharMod::GetminHp(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
				return itr->hp_min;

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
				return itr->hp_min;

	return 0;
}

uint32 CharMod::GetmaxHp(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
				return itr->hp_max;

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
				return itr->hp_max;

	return 0;
}

uint32 CharMod::GetMinStats(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid, Stats stat)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
			{
				switch (stat)
				{
				case STAT_STRENGTH:
					return itr->strength_min;
				case STAT_AGILITY:
					return itr->agility_min;
				case STAT_STAMINA:
					return itr->stamina_min;
				case STAT_INTELLECT:
					return itr->intellect_min;
				case STAT_SPIRIT:
					return itr->spirit_min;
				default:
					break;
				}
			}

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
			{
				switch (stat)
				{
				case STAT_STRENGTH:
					return itr->strength_min;
				case STAT_AGILITY:
					return itr->agility_min;
				case STAT_STAMINA:
					return itr->stamina_min;
				case STAT_INTELLECT:
					return itr->intellect_min;
				case STAT_SPIRIT:
					return itr->spirit_min;
				default:
					break;
				}
			}

	return 0;
}

uint32 CharMod::GetMaxStats(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid, Stats stat)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
			{
				switch (stat)
				{
				case STAT_STRENGTH:
					return itr->strength_max;
				case STAT_AGILITY:
					return itr->agility_max;
				case STAT_STAMINA:
					return itr->stamina_max;
				case STAT_INTELLECT:
					return itr->intellect_max;
				case STAT_SPIRIT:
					return itr->spirit_max;
				default:
					break;
				}
			}

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
			{
				switch (stat)
				{
				case STAT_STRENGTH:
					return itr->strength_max;
				case STAT_AGILITY:
					return itr->agility_max;
				case STAT_STAMINA:
					return itr->stamina_max;
				case STAT_INTELLECT:
					return itr->intellect_max;
				case STAT_SPIRIT:
					return itr->spirit_max;
				default:
					break;
				}
			}

	return 0;
}

uint32 CharMod::GetIMinOtherStats(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid, OtherStatsInt stat)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
			{
				switch (stat)
				{
				case STAT_ATTACK_POWER:
					return itr->attack_power_min;
				case STAT_SPELL_POWER:
					return itr->spell_power_min;
				case STAT_HASTE:
					return itr->haste_min;
				case STAT_ARMOR:
					return itr->armor_min;
				case STAT_RESILIENCE:
					return itr->Resilience_min;
				case STAT_HIT:
					return itr->hit_min;
				case STAT_DEFENSE_RATING:
					return itr->defense_min;
				default:
					break;
				}
			}

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
			{
				switch (stat)
				{
				case STAT_ATTACK_POWER:
					return itr->attack_power_min;
				case STAT_SPELL_POWER:
					return itr->spell_power_min;
				case STAT_HASTE:
					return itr->haste_min;
				case STAT_ARMOR:
					return itr->armor_min;
				case STAT_RESILIENCE:
					return itr->Resilience_min;
				case STAT_HIT:
					return itr->hit_min;
				case STAT_DEFENSE_RATING:
					return itr->defense_min;
				default:
					break;
				}
			}

	return 0;
}

uint32 CharMod::GetIMaxOtherStats(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid, OtherStatsInt stat)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
			{
				switch (stat)
				{
				case STAT_ATTACK_POWER:
					return itr->attack_power_max;
				case STAT_SPELL_POWER:
					return itr->spell_power_max;
				case STAT_HASTE:
					return itr->haste_max;
				case STAT_ARMOR:
					return itr->armor_max;
				case STAT_RESILIENCE:
					return itr->Resilience_max;
				case STAT_HIT:
					return itr->hit_max;
				case STAT_DEFENSE_RATING:
					return itr->defense_max;
				default:
					break;
				}
			}

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
			{
				switch (stat)
				{
				case STAT_ATTACK_POWER:
					return itr->attack_power_max;
				case STAT_SPELL_POWER:
					return itr->spell_power_max;
				case STAT_HASTE:
					return itr->haste_max;
				case STAT_ARMOR:
					return itr->armor_max;
				case STAT_RESILIENCE:
					return itr->Resilience_max;
				case STAT_HIT:
					return itr->hit_max;
				case STAT_DEFENSE_RATING:
					return itr->defense_max;
				default:
					break;
				}
			}

	return 0;
}

float CharMod::GetFMinOtherStats(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid, OtherStatsFloat stat)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
			{
				switch (stat)
				{
				case STAT_CRIT:
					return itr->crit_min;
				case STAT_DODGE:
					return itr->dodge_min;
				case STAT_PARRY:
					return itr->parry_min;
				case STAT_BLOCK:
					return itr->block_min;
				default:
					break;
				}
			}

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
			{
				switch (stat)
				{
				case STAT_CRIT:
					return itr->crit_min;
				case STAT_DODGE:
					return itr->dodge_min;
				case STAT_PARRY:
					return itr->parry_min;
				case STAT_BLOCK:
					return itr->block_min;
				default:
					break;
				}
			}

	return 0.f;
}

float CharMod::GetFMaxOtherStats(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid, OtherStatsFloat stat)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (mapid == itr->map_id)
			{
				switch (stat)
				{
				case STAT_CRIT:
					return itr->crit_max;
				case STAT_DODGE:
					return itr->dodge_max;
				case STAT_PARRY:
					return itr->parry_max;
				case STAT_BLOCK:
					return itr->block_max;
				default:
					break;
				}
			}

	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
			{
				switch (stat)
				{
				case STAT_CRIT:
					return itr->crit_max;
				case STAT_DODGE:
					return itr->dodge_max;
				case STAT_PARRY:
					return itr->parry_max;
				case STAT_BLOCK:
					return itr->block_max;
				default:
					break;
				}
			}

	return 0.f;
}

bool CharMod::CheakInMap(uint32 classid, int32 mapid)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
		if (classid == itr->class_id)
			if (itr->map_id == mapid)
				return true;

	return false;
}

bool CharMod::CheakInZoneArea(uint32 classid, int32 mapid, uint32 zoneid, uint32 areaid)
{
	for (std::vector<ClassesStatsModForMap>::const_iterator itr = ClassesStatsModForMapVec.begin(); itr != ClassesStatsModForMapVec.end(); ++itr)
	{
		if (classid == itr->class_id)
			if (itr->map_id == mapid || itr->map_id == -1)
				if ((zoneid == itr->zone_id && areaid == itr->area_id) || (zoneid == itr->zone_id && itr->area_id == 0))
					return true;
	}

	return false;
}

void CharMod::UpdateAllStatsMod(Player * player)
{
	player->UpdateMaxHealth();
	player->SetFullHealth();

	for (uint8 i = STAT_STRENGTH; i < MAX_STATS; ++i)
		player->UpdateStats(Stats(i));

	if (Unit* pet = player->GetGuardianPet())
		pet->UpdateMaxHealth();

	player->UpdateDodgePercentage();
	player->UpdateParryPercentage();
	player->UpdateBlockPercentage();
	player->UpdateCritPercentage(BASE_ATTACK);
	player->UpdateCritPercentage(OFF_ATTACK);
	player->UpdateCritPercentage(RANGED_ATTACK);
	player->UpdateAllSpellCritChances();
	player->UpdateMeleeHitChances();
	player->UpdateRangedHitChances();
	player->UpdateSpellHitChances();
	player->UpdateArmor();
	player->UpdateRating(CR_DEFENSE_SKILL);
	player->UpdateAttackPowerAndDamage(true);
	player->UpdateAttackPowerAndDamage();
	player->UpdateSpellDamageAndHealingBonus();
	player->ApplyRatingMod(CR_HASTE_MELEE, 0, true);
	player->ApplyRatingMod(CR_HASTE_RANGED, 0, true);
	player->ApplyRatingMod(CR_HASTE_SPELL, 0, true);
	player->ApplyRatingMod(CR_CRIT_TAKEN_MELEE, 0, true);
	player->ApplyRatingMod(CR_CRIT_TAKEN_RANGED, 0, true);
	player->ApplyRatingMod(CR_CRIT_TAKEN_SPELL, 0, true);

}

bool CharMod::HandleRex(Player * player)
{
	uint32 reqid = 0;
	uint32 mapid = player->GetMapId();

	if (player->IsAlive())
		return true;

	for (std::vector<RexTemp>::iterator itr = RexTempVec.begin(); itr != RexTempVec.end(); ++itr)
		if (mapid == itr->mapid)
		{
			reqid = itr->reqid;
			break;
		}

	if (reqid > 0)
	{
		if (!sReq->Check(player, reqid))
		{
			//sGCAddon->SendPacketTo(player, "SSREX_FRAME", "SHOW");
			return false;
		}

		sReq->Des(player, reqid);

		player->ResurrectPlayer(!AccountMgr::IsPlayerAccount(player->GetSession()->GetSecurity()) ? 1.0f : 0.5f);
		player->SpawnCorpseBones();
		player->SaveToDB(false, false);
		//ChatHandler(player->GetSession()).PSendSysMessage("你已经得到了大天使的庇佑！");
		return true;
	}

	return false;
}

bool CharMod::CheckRex(Player * player)
{

	if (player->IsAlive())
		return false;

	uint32 mapid = player->GetMapId();

	for (std::vector<RexTemp>::iterator itr = RexTempVec.begin(); itr != RexTempVec.end(); ++itr)
		if (mapid == itr->mapid)
			return true;

	return false;

}

class CharModPlayerScript : public PlayerScript
{
public:
	CharModPlayerScript() : PlayerScript("CharModPlayerScript") {}

	void OnLogin(Player* player, bool)
	{
		//sPCode->LoginRewPromoRewToFrom(player);
		sCharMod->ModLimit(player);
		sCharMod->InitPlayerBotNum(player);
		sPCode->InitPromoLogs(player);
	}

	void OnMapChanged(Player* player) 
	{
		uint32 mapid = player->GetMapId();
		if (sCharMod->CheakInMap(player->getClass(), mapid))
			sCharMod->UpdateAllStatsMod(player);
	}

	void OnLogout(Player* player)
	{
		sCharMod->SavePlayerBotNum(player->GetSession()->GetAccountId());
	}
};

void AddSC_CharModPlayerScript()
{
	new CharModPlayerScript();
}
