﻿#pragma execution_character_set("utf-8")
#include "playerbot.h"
#include "../Switch/Switch.h"
std::vector<uint32> BotLowGUIDVec;

void LoadBotLowGUID()
{
	BotLowGUIDVec.clear();
	std::vector<int32> BotAccountVec;
	sSwitch->GetVec(ST_BOT_COUNT, BotAccountVec);

	for (auto itr = BotAccountVec.begin(); itr != BotAccountVec.end(); itr++)
	{
		if (QueryResult result = CharacterDatabase.PQuery("SELECT guid FROM characters WHERE account = %d", *itr))
		{
			do
			{
				Field* fields = result->Fetch();
				BotLowGUIDVec.push_back(fields[0].GetUInt32());
			} while (result->NextRow());
		}
	}
}

UNORDERED_MAP<uint64, WorldSession *> PlayerBotSessionMap;

DWORD WINAPI AddPlayerBotThread(LPVOID i){ Sleep(30000); AddPlayerBot(reinterpret_cast<int>(i)); return 0; }
void AddBotThreadStart(int i){ CloseHandle(CreateThread(NULL, 0, AddPlayerBotThread, reinterpret_cast<LPVOID>(i), 0, NULL)); }

void AddPlayerBot(uint32 lowGUID, Player* owner)
{
	if (!BotLowGUIDVec.empty())
	{
		bool load = false;

		std::random_shuffle(BotLowGUIDVec.begin(), BotLowGUIDVec.end());

		for (auto itr = BotLowGUIDVec.begin(); itr != BotLowGUIDVec.end(); itr++)
		{
			auto i = PlayerBotSessionMap.find(*itr);

			if (i == PlayerBotSessionMap.end())
			{
				lowGUID = *itr;
				load = true;
				break;
			}
		}

		if (!load)
			return;
	}

	PBLoginResult res = RES_ERR_OK;

	uint64 guid = MAKE_NEW_GUID(lowGUID, 0, HIGHGUID_PLAYER);
	std::string name = "";

	if (Player* pl = ObjectAccessor::FindPlayerInOrOutOfWorld(guid))
	{
		res = RES_ERR_LOGGED;
		name = pl->GetName();
	}
	else
	{
		QueryResult result = CharacterDatabase.PQuery("SELECT account, name FROM characters WHERE guid = %u", lowGUID);

		if (!result)
			res = RES_ERR_NO_GUID;
		else
		{
			uint32 account = result->Fetch()[0].GetUInt32();
			name = result->Fetch()[1].GetString();

			if (!account)
				res = RES_ERR_NO_GUID;
			else
			{
				WorldSession *s = new WorldSession(account, NULL, SEC_PLAYER, 2, time(NULL), LOCALE_zhCN, 0, false, true);

				if (!s)
				{
					delete s;
					res = RES_ERR_NO_SESSION;
				}
				else
				{
					s->SetPlayerLoading(true);

					LoginQueryHolder *holder = new LoginQueryHolder(account, guid);

					if (!holder->Initialize())
					{
						delete holder;
						s->SetPlayerLoading(false);
						delete s;
						res = RES_ERR_NO_HOLDER;
					}
					else
					{
						QueryResultHolderFuture future = CharacterDatabase.DelayQueryHolder((SQLQueryHolder*)holder);

						while (true)
						{
							if (future.ready())
							{
								SQLQueryHolder* param;
								future.get(param);
								LoginQueryHolder* holder = (LoginQueryHolder*)param;
								s->HandlePlayerLoginFromDB((LoginQueryHolder*)param);
								future.cancel();
								break;
							}
						}

						auto itr = PlayerBotSessionMap.find(guid);

						if (itr != PlayerBotSessionMap.end())
						{
							delete itr->second;
							itr->second = s;
						}
						else
							PlayerBotSessionMap.insert(std::make_pair(guid, s));

						sLog->outString(">> 玩家[%s]作为假人上线..", name.c_str());
					}
				}
			}
		}
	}

	if (owner)
	{
		switch (res)
		{
		case RES_ERR_OK:
			ChatHandler(owner->GetSession()).PSendSysMessage("佣兵[%s]上线成功", name.c_str());
			break;
		case RES_ERR_LOGGED:
			ChatHandler(owner->GetSession()).PSendSysMessage("佣兵[%s]上线失败，该角色已在线", name.c_str());
			break;
		case RES_ERR_NO_GUID:
			ChatHandler(owner->GetSession()).PSendSysMessage("佣兵上线失败，guid为%u的角色不存在", lowGUID);
			break;
		case RES_ERR_NO_HOLDER:
			ChatHandler(owner->GetSession()).PSendSysMessage("佣兵上线失败，建立数据库查询失败");
			break;
		case RES_ERR_NO_SESSION:
			ChatHandler(owner->GetSession()).PSendSysMessage("佣兵上线失败，建立Session失败");
			break;
		default:
			break;
		}
	}
}

void RemovePlayerBot(uint64 guid)
{
	auto itr = PlayerBotSessionMap.find(guid);

	if (itr == PlayerBotSessionMap.end())
		return;

	if (itr->second)
	{
		delete itr->second;
		itr->second = NULL;
	}
		
	PlayerBotSessionMap.erase(itr);
}

void UpdatePlayerBotSessions(uint32 diff)
{
	for (auto itr = PlayerBotSessionMap.begin(); itr != PlayerBotSessionMap.end(); itr++)
	{
		Player* pl = itr->second->GetPlayer();

		if (!pl)
			continue;

		if (pl->IsBeingTeleportedNear())
		{
			WorldPacket p = WorldPacket(MSG_MOVE_TELEPORT_ACK, 8 + 4 + 4);
			p.appendPackGUID(pl->GetGUID());
			p << (uint32)0;
			p << (uint32)time(0);
			pl->GetSession()->HandleMoveTeleportAck(p);
		}
		
		if (pl->IsBeingTeleportedFar())
			pl->GetSession()->HandleMoveWorldportAckOpcode();
		else if (pl->IsInWorld())
			pl->GetSession()->HandlePlayerBotPackets();
	}
}

uint32 GetBotCount()
{
	uint32 playerCount = 0;

	SessionMap::const_iterator itr;
	for (itr = sWorld->GetAllSessions().begin(); itr != sWorld->GetAllSessions().end(); ++itr)
		if (itr->second &&
			itr->second->GetPlayer() &&
			itr->second->GetPlayer()->IsInWorld())
			playerCount++;

	return sWorld->GetPlayerCount() - playerCount;
}

bool CanLogBot()
{
	return GetBotCount() < sSwitch->GetValue(ST_BOT_COUNT);
}

void log(std::string s)
{
	sWorld->SendServerMessage(SERVER_MSG_STRING, s.c_str());
}

void log(Player* pl, std::string s)
{
	ChatHandler(pl->GetSession()).PSendSysMessage(s.c_str());
}

//ai
void BotCast(Player* bot, uint32 spell)
{
	//WorldPacket p(CMSG_CAST_SPELL);
	//p << uint8(0);
	//p << uint32(spell);
	//p << uint8(0);
	//bot->GetSession()->HandleCastSpellOpcode(p);

	//if (bot->HasSpellCooldown(spell))
		bot->CastSpell(bot->GetVictim(), spell, false);
}

void AutoCastSpell(Player* bot)
{
	if (bot->HasUnitState(UNIT_STATE_CASTING))
		return;

	Unit* victim = bot->GetVictim();

	if (!victim)
		victim = bot->SelectNearbyTarget(NULL, 50.0f);

	//if (victim && victim->GetDistance(bot) > 20.0f)
		//bot->GetMotionMaster()->MoveChase(victim, 15.0f);

	if (!victim)
		victim = bot;

	bot->SetSelection(victim->GetGUID());
	bot->SetFacingToObject(victim);

	switch (bot->getClass())
	{
	case CLASS_PRIEST:
	{
		if (!victim->HasAura(48158, bot->GetGUID()))
			BotCast(bot, 48158);

		if (!victim->HasAura(48300, bot->GetGUID()))
			BotCast(bot, 48300);

		BotCast(bot, 48127);
		BotCast(bot, 48123);
		BotCast(bot, 48135);
	}
	break;
	case CLASS_MAGE:
	{
		bot->Attack(victim, false);

		if (!bot->HasAura(42995))
			BotCast(bot, 42995);

		if (!bot->HasAura(7301))
			BotCast(bot, 7301);

		if (!victim->HasAura(42917) && bot->HasSpellCooldown(42917) && bot->GetDistance(victim) < 10.0f)
			BotCast(bot, 42917);
		
		

		switch (urand(0, 3))
		{
		case 0:
			log("0");
			BotCast(bot, 42842);
			break;
		case 1:
			log("1");
			BotCast(bot, 42833);
			break;
		case 2:
			log("2");
			BotCast(bot, 47610);
			break;
		default:
			break;
		}
	}
	break;
	default:
		break;
	}
}


class PlayerBotScript : public PlayerScript
{
public:
	PlayerBotScript() : PlayerScript("PlayerBotScript") {}

	void OnLogin(Player* bot, bool) override
	{
		//if (bot->IsPlayerBot())
		//{
		//	bot->SetBotState(STATE_MOVE_TO_NPCS);
		//	bot->TeleportTo(530, -1876, 5455, -12.43, 0);
		//	bot->SetHomeDistance(100.0f);
		//	bot->SetHomePosition(-1876, 5455, -12.43);
		//}
		//else
		//	bot->SetBotState(STATE_NONE);
	}

	void OnMapChanged(Player* bot) override
	{
		
	}

	void OnUpdateZone(Player* bot, uint32 newZone, uint32 newArea) override
	{
		
	}

	void OnEnterCombat(Player* bot, Unit* victim)
	{
		if (!bot->IsPlayerBot())
			return;
	}

	void OnClearCombat(Player* bot, Unit* victim)
	{
		if (!bot->IsPlayerBot())
			return;
	}

	void OnTimePassed_1(Player* bot) 
	{
		if (!bot->IsPlayerBot())
			return;

		AutoCastSpell(bot);
	}

	void OnTimePassed_5(Player* bot)
	{
		//if (bot->GetBotState() == STATE_MOVE_TO_NPCS)
		//{
		//	if (!bot->isMoving())
		//	{
		//		std::list<Creature*> list;
		//		std::vector<Creature*> vec;
		//		bot->GetCreatureListInGrid(list, 50);
		//		if (!list.empty())
		//		{
		//			for (std::list<Creature*>::iterator itr = list.begin(); itr != list.end(); ++itr)
		//			{
		//				Creature* c = *itr;
		//
		//				if (!c->IsAlive() || !c->HasFlag(UNIT_NPC_FLAGS, UNIT_NPC_FLAG_GOSSIP) || c->GetAreaId() != bot->GetAreaId())
		//					continue;
		//
		//				vec.push_back(c);
		//			}
		//		}
		//
		//		if (!bot->IsInHomeDistance())
		//			bot->MoveHome();
		//		else if (!vec.empty())
		//		{
		//			if (Creature* npc = vec[urand(0, vec.size() - 1)])
		//			{
		//				log("选择目标：" + npc->GetName() + "开始移动");
		//				bot->MoveToPoint(npc->GetPositionX(), npc->GetPositionY(), npc->GetPositionZ());
		//			}		
		//		}
		//	}
		//}
	}

	void OnMovementInform(Player* bot, MovementGeneratorType type, uint32 id) override
	{ 
		if (!bot->IsPlayerBot())
			return;

		if (type == POINT_MOTION_TYPE)
		{
			log("停止移动");
			bot->GetMotionMaster()->MovementExpired();
			bot->StopMoving();
		}

		//if (type == FOLLOW_MOTION_TYPE)
		//{
		//	bot->GetMotionMaster()->MovementExpired();
		//	bot->StopMoving();
		//	log("停止移动");
		//
		//	if (bot->GetDistance(bot->m_homebindX, bot->m_homebindY, bot->m_homebindZ) > 50)
		//		bot->TelePort();
		//}		
	}
};

void AddSC_PlayerBot()
{
	//new PlayerBotScript();
}

UNORDERED_MAP<uint8, std::vector<uint32>> EnterCombatSpellMap;
UNORDERED_MAP<uint8, std::vector<uint32>> AutoCastSpellMap;
UNORDERED_MAP<uint8, std::vector<uint32>> AutoCastBuffMap;

void InitBots()
{
	std::vector<uint32> vec;
	vec.push_back(48125);
	vec.push_back(48127);
	vec.push_back(48300);

	AutoCastSpellMap.insert(std::make_pair(CLASS_PRIEST, vec));
}


enum PBCombatTriggers
{
	TRIGGER_ENTER_COMBAT,
	TRIGGER_CANOT_FREE_MOVE,
	TRIGGER_DETECT_SPELL,
	TRIGGER_DETECT_ENEMY_HP,
	TRIGGER_AURA_APPLY,
	TRIGGER_AURA_REMOVE,
};

enum PBCombatActions
{
	ACTION_ATTACK,
	ACTION_CAST,
	ACTION_SAY,
	ACTION_YELL,
	ACTION_FLEE,
	ACTION_CHASE,
};

struct PBCombatTemplate
{
	PBCombatTriggers trigger;
	char* triggerParam;
	float actionChance;
	PBCombatActions action;
	std::vector<char*> actionParamVec;
};

std::vector<PBCombatTemplate> PBCombatVec;

void DoActionOnTrigger(Player* bot, Unit* victim, PBCombatTriggers trigger, std::string triggerParam)
{
	if (!bot->IsPlayerBot())
		return;

	for (auto itr = PBCombatVec.begin(); itr != PBCombatVec.end(); itr++)
	{
		if (trigger == itr->trigger && triggerParam == itr->triggerParam && roll_chance_f(itr->actionChance))
		{
			std::random_shuffle(itr->actionParamVec.begin(), itr->actionParamVec.end());

			switch (itr->action)
			{
			case ACTION_ATTACK:
				bot->Attack(victim, true);
				break;
			case ACTION_CAST:
			{
				for (auto i = itr->actionParamVec.begin(); i != itr->actionParamVec.end(); i++)
				{
					uint32 spellid = atoi(*i);

					if (bot->HasSpellCooldown(spellid))
						BotCast(bot, spellid);
				}
			}
			break;
			case ACTION_SAY:
				for (auto i = itr->actionParamVec.begin(); i != itr->actionParamVec.end(); i++)
					bot->Say(*i, LANG_UNIVERSAL);
				break;
			case ACTION_YELL:
				for (auto i = itr->actionParamVec.begin(); i != itr->actionParamVec.end(); i++)
					bot->Yell(*i, LANG_UNIVERSAL);
				break;
			case ACTION_FLEE:
				break;
			case ACTION_CHASE:
				break;
			default:
				break;
			}
		}
	}
}