﻿#include "bot_expend.h"
#include "Chat.h"
#include "DatabaseEnv.h"
#include "DBCStores.h"
#include "GameEventMgr.h"
#include "GameObjectAI.h"
#include "GossipDef.h"
#include "GridNotifiersImpl.h"
#include "InstanceScript.h"
#include "Item.h"
#include "LFG.h"
#include "LFGMgr.h"
#include "Log.h"
#include "Mail.h"
#include "MapManager.h"
#include "MotionMaster.h"
#include "ObjectMgr.h"
#include "PathGenerator.h"
#include "ScriptedGossip.h"
#include "SpellAuraEffects.h"
#include "TemporarySummon.h"
#include "Transport.h"
#include "World.h"
#include "ScriptMgr.h"
#include "..\Custom\CommonFunc\CommonFunc.h"
#include "..\Custom\Reward\Reward.h"
#include "botmgr.h"
#include "..\Custom\GCAddon\GCAddon.h"

#pragma execution_character_set("utf-8")

typedef std::multimap<uint32 /*entry*/, BotExpFaBaoData *> BotExpFaBaoDataMap;
BotExpFaBaoDataMap _botFaBaoData;

typedef std::vector<BotExpFaBaoTemp> BotExpFaBaoTempVec;
BotExpFaBaoTempVec _BotExpFaBaoTempVec;

bot_exp::bot_exp()
{

}

bot_exp::~bot_exp()
{

}

void bot_exp::AddGossipItemFor(Player* player, uint32 icon, const std::string& text, uint32 sender, uint32 action)
{
	player->PlayerTalkClass->GetGossipMenu().AddMenuItem(-1, icon, text, sender, action, "", 0);
}


void bot_exp::Load()
{
	_BotExpFaBaoTempVec.clear();

	QueryResult result = WorldDatabase.PQuery("SELECT 法宝ID, 变形模型ID, 插槽位 FROM _佣兵_法宝配置表");

	if (result)
	{
		uint32 count = 0;
		do
		{
			Field* fields = result->Fetch();
			BotExpFaBaoTemp Temp;
			Temp.itemid = fields[0].GetUInt32();
			Temp.displayid = fields[1].GetUInt32();
			Temp.slotid = fields[2].GetUInt32();
			_BotExpFaBaoTempVec.push_back(Temp);
			++count;
		} while (result->NextRow());

		sLog->outString("_佣兵_法宝配置表...加载%u条数据", count);
	}
}

void bot_exp::LoadLog()
{
	QueryResult result = CharacterDatabase.PQuery("SELECT 玩家GUID, NPCBOT职业ID, 法宝1ID, 法宝2ID, 法宝3ID, 法宝4ID, 法宝5ID FROM _佣兵_法宝记录表");
	
	if (result)
	{
		BotExpFaBaoData * fabaoData;
		uint32 count = 0;
		do
		{
			Field* fields = result->Fetch();

			uint32 owner = fields[0].GetUInt32();
			uint32 botclass = fields[1].GetUInt32();
			uint32 fabaoid1 = fields[2].GetUInt32();
			uint32 fabaoid2 = fields[3].GetUInt32();
			uint32 fabaoid3 = fields[4].GetUInt32();
			uint32 fabaoid4 = fields[5].GetUInt32();
			uint32 fabaoid5 = fields[6].GetUInt32();


			fabaoData = new BotExpFaBaoData(owner, botclass);
			fabaoData->fabao[0] = fabaoid1;
			fabaoData->fabao[1] = fabaoid2;
			fabaoData->fabao[2] = fabaoid3;
			fabaoData->fabao[3] = fabaoid4;
			fabaoData->fabao[4] = fabaoid5;
			_botFaBaoData.insert({ owner, fabaoData });
			++count;
		} while (result->NextRow());

		sLog->outString("_佣兵_法宝记录表...加载%u条数据", count);
	}
}

const std::string& bot_exp::LocalizedNpcText(uint32 textId)
{
	if (GossipText const* nt = sObjectMgr->GetGossipText(textId))
			return nt->Options[0].Text_0;

	{
		static std::map<uint32, std::string> unk_botstrings;

		if (!unk_botstrings.count(textId))
		{

			std::ostringstream msg;
			msg << "<未知文本编号：" << textId << ">";
			unk_botstrings[textId] = msg.str();
		}

		return unk_botstrings[textId];
	}
}

BotExpFaBaoData const * bot_exp::SelectNpcBotFaBaoData(uint32 owner, uint32 botclass)
{
	for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
	{
		if (itr->first == owner)
			if (itr->second->botclass == botclass)
				return itr->second;
	}

	return nullptr;
}

void bot_exp::SendAllNpcBotFaBaoData(Player * player)
{
	uint32 pguid = player->GetGUIDLow();

	for (uint32 i = CLASS_WARRIOR; i < MAX_CLASSES; ++i)
	{
		if (i == 10)
			continue;

		BotExpFaBaoData const * data = SelectNpcBotFaBaoData(pguid, i);

		std::ostringstream msg;

		msg << data->botclass;

		for (uint8 i = 0; i != BOT_法宝_插槽最大值; ++i)
			msg << "#" << i+1 << "#" << data->fabao[i];

		sGCAddon->SendPacketTo(player, "ALLFABAODATA", msg.str());
	}
}

void bot_exp::SendNpcBotFaBaoData(Player * player, uint32 botclass, uint32 slot, uint32 itemid, bool isEquip)
{
	uint32 pguid = player->GetGUIDLow();

	std::ostringstream msg;

	msg << botclass << "#" << slot << "#" << itemid;

	std::string opcode = isEquip ? "EQUIPFABAO" : "UNEQUIPFABAO";

	sGCAddon->SendPacketTo(player, opcode, msg.str());
}

void bot_exp::EquipFaBao(Player * player, uint32 botclass, uint32 slot, uint32 itemid)
{
	if (player->IsInCombat())
	{
		ChatHandler(player->GetSession()).PSendSysMessage("你正处在战斗中！");
		return;
	}
	
	if (botclass == CLASS_NONE || botclass > CLASS_DRUID || botclass == 10 || slot > BOT_法宝_插槽最大值)
		return;

	//可能还要做一个计时器，不然镶嵌可不好

	uint32 pguid = player->GetGUIDLow();

	uint32 slotid = GetSlotidFromFaBao(itemid);
	if (slotid != slot)
	{
		std::string str = LocalizedNpcText(BOT_TEXT_扩展_法宝_槽位_错误);
		ChatHandler(player->GetSession()).PSendSysMessage(str.c_str(), sObjectMgr->GetItemNameById(itemid).c_str(), slotid);
		return;
	}

	//sLog->outString("botclass is %u | slot is %u | itemid is %u", botclass, slot, itemid);

	for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
	{
		if (itr->first == pguid && itr->second->botclass == botclass)
		{
			uint32 oldfabao = itr->second->fabao[slot - 1];
			if (ItemTemplate const * temp = sObjectMgr->GetItemTemplate(oldfabao))
				sRew->RewItem(player, oldfabao, 1);
			itr->second->fabao[slot - 1] = itemid;

			uint32 count = 1;
			player->DestroyItemCount(itemid, count, true);
			if (BotMgr * botmgr = player->GetBotMgr())
				botmgr->HandleBotExFaBao(pguid, botclass);

			SendNpcBotFaBaoData(player, botclass, slot, itemid, true);
			break;
		}
	}
}

void bot_exp::UnEquipFaBao(Player * player, uint32 botclass, uint32 slot)
{
	if (player->IsInCombat())
	{
		ChatHandler(player->GetSession()).PSendSysMessage("你正处在战斗中！");
		return;
	}

	if (botclass == CLASS_NONE || botclass > CLASS_DRUID || botclass == 10 || slot > BOT_法宝_插槽最大值)
		return;

	uint32 pguid = player->GetGUIDLow();

	for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
	{
		if (itr->first == pguid && itr->second->botclass == botclass)
		{
			uint32 oldfabao = itr->second->fabao[slot - 1];
			if (ItemTemplate const * temp = sObjectMgr->GetItemTemplate(oldfabao))
				sRew->RewItem(player, oldfabao, 1);

			itr->second->fabao[slot - 1] = 0;

			if (BotMgr * botmgr = player->GetBotMgr())
				botmgr->HandleBotExFaBao(pguid, botclass);

			SendNpcBotFaBaoData(player, botclass, slot, 0, false);
			break;
		}
	}
}

uint32 * bot_exp::GetFaBaoId(uint32 owner, uint32 botclass)
{
	for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
	{
		if (itr->first == owner)
			if (itr->second->botclass == botclass)
				return itr->second->fabao;
	}

	return nullptr;
}

uint32 bot_exp::GetDisplayidFromFaBao(uint32 itemid)
{
	for (std::vector<BotExpFaBaoTemp>::iterator itr = _BotExpFaBaoTempVec.begin(); itr != _BotExpFaBaoTempVec.end(); ++itr)
	{
		if (itr->itemid == itemid)
			return itr->displayid;
	}

	return 0;
}

uint32 bot_exp::GetSlotidFromFaBao(uint32 itemid)
{
	for (std::vector<BotExpFaBaoTemp>::iterator itr = _BotExpFaBaoTempVec.begin(); itr != _BotExpFaBaoTempVec.end(); ++itr)
	{
		if (itr->itemid == itemid)
			return itr->slotid;
	}

	return 0;
}

uint32 bot_exp::GetGossipSenderVal(Classes botclass)
{
	switch (botclass)
	{
	case CLASS_WARRIOR:
		return GOSSIP_SENDER_扩展_装备法宝_战士;
	case CLASS_PALADIN:
		return GOSSIP_SENDER_扩展_装备法宝_圣骑;
	case CLASS_HUNTER:
		return GOSSIP_SENDER_扩展_装备法宝_猎人;
	case CLASS_ROGUE:
		return GOSSIP_SENDER_扩展_装备法宝_盗贼;
	case CLASS_PRIEST:
		return GOSSIP_SENDER_扩展_装备法宝_牧师;
	case CLASS_DEATH_KNIGHT:
		return GOSSIP_SENDER_扩展_装备法宝_死骑;
	case CLASS_SHAMAN:
		return GOSSIP_SENDER_扩展_装备法宝_萨满;
	case CLASS_MAGE:
		return GOSSIP_SENDER_扩展_装备法宝_法师;
	case CLASS_WARLOCK:
		return GOSSIP_SENDER_扩展_装备法宝_术士;
	case CLASS_DRUID:
		return GOSSIP_SENDER_扩展_装备法宝_德鲁伊;
	default:
		break;
	}

	return 0;
}

void bot_exp::SaveDB(Player * player)
{
	uint32 pguid = player->GetGUIDLow();

	for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
	{
		if (itr->first != pguid)
			continue;

		uint32 botclass = itr->second->botclass;
		uint32 * itemid = itr->second->fabao;
		PreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_NPCBOT_EXP_FABAO_DATA);
		stmt->setUInt32(0, pguid);
		stmt->setUInt32(1, botclass);
		for (uint32 i = 0; i < BOT_法宝_插槽最大值; i++)
		{
			stmt->setUInt32((i + 2), itemid[i]);
		}
		
		CharacterDatabase.Execute(stmt);
	}
}

bool bot_exp::CanEquipFaBao(uint32 itemid, uint32 botclass, uint32 slot)
{
	if (!IsNpcBotFaBao(itemid))
		return false;

	uint32 botclassMask = 1 << (botclass - 1);

	ItemTemplate const * itemtemp = sObjectMgr->GetItemTemplate(itemid);

	if (!itemtemp || !(itemtemp->AllowableClass & botclassMask))
		return false;

	bool IsCan = false;

	for (std::vector<BotExpFaBaoTemp>::iterator itr = _BotExpFaBaoTempVec.begin(); itr != _BotExpFaBaoTempVec.end(); ++itr)
	{
		if (itr->itemid == itemid)
		{
			if (slot == slot)
				IsCan = true;

			break;
		}
	}

	return IsCan;
}

bool bot_exp::GenerateFaBaoMgrGossip(Player * player, Creature * creature, uint32 sender, uint32 action)
{
	uint32 playerguid = player->GetGUIDLow();

	sLog->outString("bot_exp::GenerateFaBaoMgrGossip sender = 【%u】 | action = 【%u】 ", sender, action);

	
	if (sender > GOSSIP_SENDER_扩展_法宝管理 && sender < GOSSIP_SENDER_扩展_法宝管理_最大值)
	{
		uint32 botclass = sender - GOSSIP_SENDER_扩展_法宝管理;
		uint32 * fabaoids = GetFaBaoId(playerguid, botclass);

		if (fabaoids == nullptr)
		{

		}
		else
		{
			for (uint8 i = 0; i < BOT_法宝_插槽最大值; i++)
			{
				std::ostringstream str;
				uint32 fabaoid = fabaoids[i];
				uint32 textid = BOT_TEXT_扩展_法宝_槽位 + (botclass <= CLASS_WARLOCK ? (botclass - 1) : (botclass - 2)) * 5 + (i + 1);
				str << LocalizedNpcText(textid);
				if (fabaoid > 0)
				{
					str << sCF->GetItemIcon(fabaoid, 18, 18, 0, 0) << sCF->GetItemLink(fabaoid);
					player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, str.str(), GOSSIP_SENDER_扩展_法宝管理界面 + botclass, GOSSIP_ACTION_INFO_DEF + i);
				}
				else
				{
					str << LocalizedNpcText(BOT_TEXT_扩展_未装备法宝);
					player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, str.str(), GOSSIP_SENDER_扩展_法宝管理界面 + botclass, GOSSIP_ACTION_INFO_DEF + i);
				}
			}
		}

		AddGossipItemFor(player, GOSSIP_ICON_CHAT, LocalizedNpcText(BOT_TEXT_扩展_返回), GOSSIP_SENDER_扩展_返回主菜单, GOSSIP_ACTION_INFO_DEF);
	}

	if (sender > GOSSIP_SENDER_扩展_法宝管理界面 && sender < GOSSIP_SENDER_扩展_法宝管理界面_最大值)
	{
		uint32 botclass = sender - GOSSIP_SENDER_扩展_法宝管理界面;
		uint32 botclassMask = 1 << (botclass - 1);

		uint32 fabaoslot = action - GOSSIP_ACTION_INFO_DEF;
		uint32 * fabaoids = GetFaBaoId(playerguid, botclass);
		uint32 fabaoid = fabaoids[fabaoslot];

		if (fabaoids == nullptr)
		{

		}
		else
		{
			std::ostringstream str;
			str << LocalizedNpcText(BOT_TEXT_扩展_当前法宝);

			if (fabaoids[fabaoslot] > 0)
				str << sCF->GetItemIcon(fabaoid, 18, 18, 0, 0) << sCF->GetItemLink(fabaoid);
			else
				str << LocalizedNpcText(BOT_TEXT_扩展_未装备法宝);

			player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, str.str(), GOSSIP_SENDER_扩展_关闭对话, GOSSIP_ACTION_INFO_DEF);
			if (fabaoids[fabaoslot] > 0)
				player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, LocalizedNpcText(BOT_TEXT_扩展_取下法宝), GOSSIP_SENDER_扩展_法宝管理_取下法宝 + botclass, GOSSIP_ACTION_INFO_DEF + fabaoslot);
			AddGossipItemFor(player, GOSSIP_ICON_CHAT, LocalizedNpcText(BOT_TEXT_扩展_返回), GOSSIP_SENDER_扩展_返回法宝管理, GOSSIP_ACTION_INFO_DEF + botclass);
			player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, LocalizedNpcText(BOT_TEXT_扩展_可用法宝), GOSSIP_SENDER_扩展_关闭对话, GOSSIP_ACTION_INFO_DEF);

			std::set<uint32> itemList, idsList;
			for (uint8 i = INVENTORY_SLOT_ITEM_START; i != INVENTORY_SLOT_ITEM_END; i++)
			{
				if (Item const* pItem = player->GetItemByPos(INVENTORY_SLOT_BAG_0, i))
				{
					uint32 itemid = pItem->GetEntry();
					uint32 item_AllowableClass = pItem->GetTemplate()->AllowableClass;
					if ((GetSlotidFromFaBao(itemid) == fabaoslot + 1) && IsNpcBotFaBao(itemid) && (item_AllowableClass & botclassMask) && (pItem->GetItemRandomPropertyId() == 0 ? idsList.find(pItem->GetEntry()) == idsList.end() : true))
					{
						itemList.insert(pItem->GetGUIDLow());
						idsList.insert(itemid);
					}
				}
			}

			for (uint8 i = INVENTORY_SLOT_BAG_START; i != INVENTORY_SLOT_BAG_END; i++)
			{
				if (Bag const* pBag = player->GetBagByPos(i))
				{
					for (uint32 j = 0; j != pBag->GetBagSize(); j++)
					{
						if (Item const* pItem = player->GetItemByPos(i, j))
						{
							uint32 itemid = pItem->GetEntry();
							uint32 item_AllowableClass = pItem->GetTemplate()->AllowableClass;
							if ((GetSlotidFromFaBao(itemid) == fabaoslot + 1) && IsNpcBotFaBao(itemid) && (item_AllowableClass & botclassMask) && (pItem->GetItemRandomPropertyId() == 0 ? idsList.find(pItem->GetEntry()) == idsList.end() : true))
							{
								itemList.insert(pItem->GetGUIDLow());
								idsList.insert(itemid);
							}
						}
					}
				}
			}

			if (itemList.empty())
			{
				AddGossipItemFor(player, GOSSIP_ICON_CHAT, LocalizedNpcText(BOT_TEXT_NOTHING_TO_GIVE), GOSSIP_SENDER_扩展_关闭对话, GOSSIP_ACTION_INFO_DEF);
			}
			else
			{
				uint32 counter = 0;
				uint32 maxcounter = BOT_GOSSIP_MAX_ITEMS - 4; //当前法宝, 取下法宝, 可用法宝, 返回
				Item * item = nullptr;

				for (std::set<uint32>::const_iterator itr = itemList.begin(); itr != itemList.end() && counter < maxcounter; ++itr)
				{
					bool found = false;
					for (uint8 i = INVENTORY_SLOT_ITEM_START; i != INVENTORY_SLOT_ITEM_END; i++)
					{
						item = player->GetItemByPos(INVENTORY_SLOT_BAG_0, i);
						if (item && item->GetGUIDLow() == (*itr))
						{
							std::ostringstream name;
							name << sCF->GetItemIcon(item->GetEntry(), 18, 18, 0, 0) << sCF->GetItemLink(item, player->GetSession());
							AddGossipItemFor(player, GOSSIP_ICON_CHAT, name.str().c_str(), GetGossipSenderVal(Classes(botclass)) + fabaoslot, GOSSIP_ACTION_INFO_DEF + item->GetEntry());
							++counter;
							found = true;
							break;
						}
					}

					if (found)
						continue;

					for (uint8 i = INVENTORY_SLOT_BAG_START; i != INVENTORY_SLOT_BAG_END; i++)
					{
						if (Bag const* pBag = player->GetBagByPos(i))
						{
							for (uint32 j = 0; j != pBag->GetBagSize(); j++)
							{
								item = player->GetItemByPos(i, j);
								if (item && item->GetGUIDLow() == (*itr))
								{
									std::ostringstream name;
									name << sCF->GetItemIcon(item->GetEntry(), 18, 18, 0, 0) << sCF->GetItemLink(item, player->GetSession());
									AddGossipItemFor(player, GOSSIP_ICON_CHAT, name.str().c_str(), GetGossipSenderVal(Classes(botclass)) + fabaoslot, GOSSIP_ACTION_INFO_DEF + item->GetEntry());
									++counter;
									found = true;
									break;
								}
							}
						}

						if (found)
							break;
					}

					if (found)
						continue;
				}
			}

		}
	}

	return true;
}

void bot_exp::CreateBotExpFaBaoData(Player * player)
{
	uint32 pguid = player->GetGUIDLow();

	for (uint32 i = CLASS_WARRIOR; i < MAX_CLASSES; ++i)
	{
		if (i == 10)
			continue;

		BotExpFaBaoData const * data = SelectNpcBotFaBaoData(pguid, i);

		if (!data)
		{
			PreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_NPCBOT_EXP_FABAO_DATA);
			stmt->setUInt32(0, pguid);
			stmt->setUInt32(1, i);
			stmt->setUInt32(2, 0);
			stmt->setUInt32(3, 0);
			stmt->setUInt32(4, 0);
			stmt->setUInt32(5, 0);
			stmt->setUInt32(6, 0);
			CharacterDatabase.Execute(stmt);

			BotExpFaBaoData * temp = new BotExpFaBaoData(pguid, i);
			_botFaBaoData.insert({ pguid, temp });
		}
	}
}

bool bot_exp::IsNpcBotFaBao(uint32 itemid)
{
	for (std::vector<BotExpFaBaoTemp>::iterator itr = _BotExpFaBaoTempVec.begin(); itr != _BotExpFaBaoTempVec.end(); ++itr)
	{
		if (itr->itemid == itemid)
			return true;
	}

	return false;
}

bool bot_exp::OnGossipHello(Player * player, Creature * creature)
{
	if (!player || !creature)
	{
		player->CLOSE_GOSSIP_MENU();
		return true;
	}

	player->PlayerTalkClass->ClearMenus();
	uint32 pguid = player->GetGUIDLow();
	uint32 j = BOT_TEXT_扩展_战士法宝;
	for (uint32 i = GOSSIP_SENDER_扩展_法宝管理_战士; i < GOSSIP_SENDER_扩展_法宝管理_最大值; ++i)
	{
		if (i == GOSSIP_SENDER_扩展_法宝管理 + 10)
			continue;

		uint32 botclass = i - GOSSIP_SENDER_扩展_法宝管理;
		player->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, LocalizedNpcText(j), i, GOSSIP_ACTION_INFO_DEF);
		++j;
	}

	player->SEND_GOSSIP_MENU(creature->GetEntry(), creature->GetGUID());

	return true;
}

bool bot_exp::OnGossipSelect(Player* player, Creature* creature, uint32 sender, uint32 action)
{
	if (!player || !creature)
	{
		player->CLOSE_GOSSIP_MENU();
		return true;
	}

	sLog->outString(" bot_exp::OnGossipSelect sender = 【%u】 | action = 【%u】 ", sender, action);

	player->PlayerTalkClass->ClearMenus();

	int32 fabaoslot = -1;
	uint32 bot_class = 0;

	if (sender >= GOSSIP_SENDER_扩展_装备法宝_战士 && sender < GOSSIP_SENDER_扩展_装备法宝_战士_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_战士;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 0 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_圣骑 && sender < GOSSIP_SENDER_扩展_装备法宝_圣骑_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_圣骑;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 1 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_猎人 && sender < GOSSIP_SENDER_扩展_装备法宝_猎人_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_猎人;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 2 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_盗贼 && sender < GOSSIP_SENDER_扩展_装备法宝_盗贼_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_盗贼;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 3 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_牧师 && sender < GOSSIP_SENDER_扩展_装备法宝_牧师_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_牧师;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 4 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_死骑 && sender < GOSSIP_SENDER_扩展_装备法宝_死骑_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_死骑;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 5 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_萨满 && sender < GOSSIP_SENDER_扩展_装备法宝_萨满_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_萨满;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 6 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_法师 && sender < GOSSIP_SENDER_扩展_装备法宝_法师_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_法师;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 7 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_术士 && sender < GOSSIP_SENDER_扩展_装备法宝_术士_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_术士;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 8 + fabaoslot);
	}
	if (sender >= GOSSIP_SENDER_扩展_装备法宝_德鲁伊 && sender < GOSSIP_SENDER_扩展_装备法宝_德鲁伊_MAX)
	{
		fabaoslot = sender - GOSSIP_SENDER_扩展_装备法宝_德鲁伊;
		bot_class = sender - (GOSSIP_SENDER_扩展_装备法宝 + BOT_法宝_插槽最大值 * 9 + fabaoslot);
	}

	if (sender > GOSSIP_SENDER_扩展_装备法宝 && sender < GOSSIP_SENDER_扩展_装备法宝_最大值)
	{
		if (fabaoslot < 0)
			sLog->outError("【佣兵法宝功能】发生对话错误！！");

		sender = sender - fabaoslot;
	}



	bool subMenu = false;

	switch (sender)
	{
	case GOSSIP_SENDER_扩展_返回法宝管理:
	{
		uint32 botclass = action - GOSSIP_ACTION_INFO_DEF;

		subMenu = true;

		if (GenerateFaBaoMgrGossip(player, creature, GOSSIP_SENDER_扩展_法宝管理 + botclass, action)){}
	}
		break;
	case GOSSIP_SENDER_扩展_返回主菜单:
		OnGossipHello(player, creature);
		return true;
	case GOSSIP_SENDER_扩展_法宝管理_战士:
	case GOSSIP_SENDER_扩展_法宝管理_圣骑:
	case GOSSIP_SENDER_扩展_法宝管理_猎人:
	case GOSSIP_SENDER_扩展_法宝管理_盗贼:
	case GOSSIP_SENDER_扩展_法宝管理_牧师:
	case GOSSIP_SENDER_扩展_法宝管理_死骑:
	case GOSSIP_SENDER_扩展_法宝管理_萨满:
	case GOSSIP_SENDER_扩展_法宝管理_法师:
	case GOSSIP_SENDER_扩展_法宝管理_术士:
	case GOSSIP_SENDER_扩展_法宝管理_德鲁伊:
	{
		subMenu = true;

		if (GenerateFaBaoMgrGossip(player, creature, sender, action)){}

		break;
	}
	case GOSSIP_SENDER_扩展_法宝管理界面_战士法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_圣骑法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_猎人法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_盗贼法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_牧师法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_死骑法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_萨满法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_法师法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_术士法宝:
	case GOSSIP_SENDER_扩展_法宝管理界面_德鲁伊法宝:
	{
		subMenu = true;

		if (GenerateFaBaoMgrGossip(player, creature, sender, action)){}

		break;
	}
	case GOSSIP_SENDER_扩展_装备法宝:
		break;
	case GOSSIP_SENDER_扩展_装备法宝_战士:
	case GOSSIP_SENDER_扩展_装备法宝_圣骑:
	case GOSSIP_SENDER_扩展_装备法宝_猎人:
	case GOSSIP_SENDER_扩展_装备法宝_盗贼:
	case GOSSIP_SENDER_扩展_装备法宝_牧师:
	case GOSSIP_SENDER_扩展_装备法宝_死骑:
	case GOSSIP_SENDER_扩展_装备法宝_萨满:
	case GOSSIP_SENDER_扩展_装备法宝_法师:
	case GOSSIP_SENDER_扩展_装备法宝_术士:
	case GOSSIP_SENDER_扩展_装备法宝_德鲁伊:
	{
		if (bot_class == CLASS_NONE)
		{
			sLog->outError("【佣兵法宝功能】| 玩家GUID：【%u】| 玩家名称：【%s】| 佣兵职业为无！", player->GetGUIDLow(), player->GetName().c_str());
			break;
		}

		uint32 itemid = action - GOSSIP_ACTION_INFO_DEF;
		uint32 pguid = player->GetGUIDLow();

		uint32 slotid = GetSlotidFromFaBao(itemid);
		if (slotid != uint32(fabaoslot + 1))
		{
			std::string str = LocalizedNpcText(BOT_TEXT_扩展_法宝_槽位_错误);
			ChatHandler(player->GetSession()).PSendSysMessage(str.c_str(), sObjectMgr->GetItemNameById(itemid).c_str(), slotid);
			break;
		}
			

		for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
		{
			if (itr->first == pguid && itr->second->botclass == bot_class)
			{
				uint32 oldfabao = itr->second->fabao[fabaoslot];
				if (ItemTemplate const * temp = sObjectMgr->GetItemTemplate(oldfabao))
					sRew->RewItem(player, oldfabao, 1);
				itr->second->fabao[fabaoslot] = itemid;

				uint32 count = 1;
				player->DestroyItemCount(itemid, count, true);
				if (BotMgr * botmgr = player->GetBotMgr())
					botmgr->HandleBotExFaBao(pguid, bot_class);
				break;
			}
		}

		subMenu = true;

		if (GenerateFaBaoMgrGossip(player, creature, GOSSIP_SENDER_扩展_法宝管理界面 + bot_class, GOSSIP_ACTION_INFO_DEF + fabaoslot)){}
	}
		break;
	case GOSSIP_SENDER_扩展_关闭对话:
		break;
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝:
		break;
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_战士:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_圣骑:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_猎人:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_盗贼:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_牧师:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_死骑:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_萨满:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_法师:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_术士:
	case GOSSIP_SENDER_扩展_法宝管理_取下法宝_德鲁伊:
	{
		uint32 botclass = sender - GOSSIP_SENDER_扩展_法宝管理_取下法宝;
		uint32 slot = action - GOSSIP_ACTION_INFO_DEF;
		uint32 pguid = player->GetGUIDLow();

		for (BotExpFaBaoDataMap::const_iterator itr = _botFaBaoData.begin(); itr != _botFaBaoData.end(); ++itr)
		{
			if (itr->first == pguid && itr->second->botclass == botclass)
			{
				uint32 oldfabao = itr->second->fabao[slot];
				if (ItemTemplate const * temp = sObjectMgr->GetItemTemplate(oldfabao))
					sRew->RewItem(player, oldfabao, 1);

				itr->second->fabao[slot] = 0;

				if (BotMgr * botmgr = player->GetBotMgr())
					botmgr->HandleBotExFaBao(pguid, botclass);
				break;
			}
		}

		subMenu = true;

		if (GenerateFaBaoMgrGossip(player, creature, GOSSIP_SENDER_扩展_法宝管理界面 + botclass, GOSSIP_ACTION_INFO_DEF + slot)){}
	}
		break;
	default:
		break;
	}
	
	if (subMenu)
		player->SEND_GOSSIP_MENU(creature->GetEntry(), creature->GetGUID());
	else
		player->CLOSE_GOSSIP_MENU();

	return true;
}

class bot_exp_fabao : public CreatureScript
{
public:
	bot_exp_fabao() : CreatureScript("bot_exp_fabao") { }

	bool OnGossipHello(Player* player, Creature* creature) override
	{
		if(sbotexp->OnGossipHello(player, creature))
			return true;

		return false;
	}

	bool OnGossipSelect(Player* player, Creature* creature, uint32 sender, uint32 action) override
	{
		if (sbotexp->OnGossipSelect(player, creature, sender, action))
			return true;

		return false;
	}
};

class bot_exp_player : public PlayerScript
{
public:
	bot_exp_player() : PlayerScript("bot_exp_player") { }

	void OnLogin(Player* player, bool /*first*/) override
	{
		sbotexp->CreateBotExpFaBaoData(player);
	}
};

void AddSC_botexp()
{
	new bot_exp_fabao();
	new bot_exp_player();
}